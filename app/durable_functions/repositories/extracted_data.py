from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from durable_functions.utils.exceptions.entity import EntityNotFoundError
from models.qual_extracted_data import QualExtractedData
from schemas import ExtractedData

from .conversation import ConversationRepository


__all__ = ['ExtractedDataRepository']


class ExtractedDataRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def update(self, extracted_data: ExtractedData):
        """
        Upsert extracted data of a specified source type for a conversation.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(extracted_data.conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(extracted_data.conversation_id))

        extracted_data_db = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == extracted_data.data_source_type,
                )
            )
        ).scalar_one_or_none()

        if extracted_data_db:
            for key, val in extracted_data.model_dump_for_db(exclude_none=True).items():
                setattr(extracted_data_db, key, val)
        else:
            extracted_data_db = QualExtractedData(
                QualConversationId=conversation_internal_id, **extracted_data.model_dump_for_db()
            )
            self.db_session.add(extracted_data_db)

        await self.db_session.flush()
