from datetime import date, datetime
from typing import Any, Literal

from pydantic import Field, field_serializer, model_validator

from constants.message import OptionType
from core import json
from core.schemas import CustomModel
from durable_functions.utils.models import FinalExtractionDataResults


class BaseOption(CustomModel):
    @model_validator(mode='before')
    @classmethod
    def validate_json_str(cls, value: Any) -> Any:
        if isinstance(value, str):
            try:
                result = json.loads(value)
                result['type'] = OptionType(result['type'])
                return result
            except Exception:
                raise ValueError('Invalid JSON string for option')
        return value


class ClientNameOption(BaseOption):
    type: Literal[OptionType.CLIENT_NAME] = Field(default=OptionType.CLIENT_NAME, frozen=True)
    client_name: str


class LDMFCountryOption(BaseOption):
    type: Literal[OptionType.LDMF_COUNTRY] = Field(default=OptionType.LDMF_COUNTRY, frozen=True)
    ldmf_country: str


class DatePickerOption(BaseOption):
    type: Literal[OptionType.DATES] = Field(default=OptionType.DATES, frozen=True)
    start_date: date | None
    end_date: date | None

    serialize_start_date = field_serializer('start_date')(CustomModel.serialize_date)
    serialize_end_date = field_serializer('end_date')(CustomModel.serialize_date)


def format_extracted_data_message(extraction_data: FinalExtractionDataResults) -> str:
    """
    Format extracted data into a user-friendly system message content.

    Args:
        extraction_data: The extracted data results

    Returns:
        Formatted message content as HTML string
    """
    message_parts = ["<p>Great! I've successfully extracted the following information from your document:</p>"]

    # Client Names
    if extraction_data.client_names:
        client_names_str = ', '.join(extraction_data.client_names)
        message_parts.append(f'<p><strong>Client Name(s):</strong> {client_names_str}</p>')

    # Lead Member Firm Countries
    if extraction_data.lead_member_countries:
        countries_str = ', '.join(extraction_data.lead_member_countries)
        message_parts.append(f'<p><strong>Lead Member Firm Country/Countries:</strong> {countries_str}</p>')

    # Engagement Dates
    if extraction_data.periods:
        period = extraction_data.periods[0]  # Use first period as done in the main function
        if period.start_date or period.end_date:
            date_info = []
            if period.start_date:
                date_info.append(f'Start: {period.start_date}')
            if period.end_date:
                date_info.append(f'End: {period.end_date}')
            dates_str = ' | '.join(date_info)
            message_parts.append(f'<p><strong>Engagement Dates:</strong> {dates_str}</p>')

    # Objective and Scope
    if extraction_data.objective_and_scope:
        message_parts.append(f'<p><strong>Objective & Scope:</strong> {extraction_data.objective_and_scope}</p>')

    # Outcomes
    if extraction_data.outcomes:
        message_parts.append(f'<p><strong>Outcomes:</strong> {extraction_data.outcomes}</p>')

    # Add next steps
    message_parts.append(
        "<p>I'll use this information to help create your qual. If you need to make any corrections or add more details, please let me know!</p>"
    )

    return ''.join(message_parts)


def generate_options(extraction_data: FinalExtractionDataResults) -> list[dict[str, Any]]:
    options = []
    # Client Names
    if extraction_data.client_names:
        options.extend(list(ClientNameOption(client_name=name).model_dump() for name in extraction_data.client_names))
        return options
    # Lead Member Firm Countries
    if extraction_data.lead_member_countries:
        options.extend(
            list(
                LDMFCountryOption(ldmf_country=country).model_dump()
                for country in extraction_data.lead_member_countries
            )
        )
        return options
    # Engagement Dates
    if extraction_data.periods:
        for period in extraction_data.periods:
            if period.start_date or period.end_date:
                start_date = datetime.strptime(period.start_date, '%Y-%m-%d').date() if period.start_date else None
                end_date = datetime.strptime(period.end_date, '%Y-%m-%d').date() if period.end_date else None
                options.append(DatePickerOption(start_date=start_date, end_date=end_date).model_dump())
                return options
    return options
