from unittest.mock import AsyncMock, call

import pytest

from constants.extracted_data import <PERSON><PERSON>tat<PERSON>
from constants.message import (
    CLIENT_NAME_MULTIPLE_OPTIONS,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    NEED_INFO_CLIENT_NAME,
    NEED_INFO_LDMF_COUNTRY,
    NEED_INFO_OUTCOMES,
)
from repositories import LDMFCountriesRepository, QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest
from schemas.confirmed_data import ConfirmedData
from schemas.quals_clients import ClientSearchItem, ClientSearchResponse
from services.extracted_data.handlers import (
    OUTCOMES_AGGREGATED_QUESTION,
    ClientNameHandler,
    LDMFCountryHandler,
    OutcomesHandler,
)


@pytest.fixture
def mock_quals_clients_repository():
    return AsyncMock(spec=QualsClientsRepository)


@pytest.fixture
def client_name_handler(mock_quals_clients_repository):
    return ClientNameHandler(quals_clients_repository=mock_quals_clients_repository)


@pytest.fixture
def mock_ldmf_countries_repository():
    repository_mock = AsyncMock(spec=LDMFCountriesRepository)
    repository_mock.get_ldmf_countries.return_value = [
        {'memberFirmId': 2470, 'name': 'Germany', 'id': 158},
        {'memberFirmId': 0, 'name': 'Chad', 'id': 141},
        {'memberFirmId': 2768, 'name': 'United States', 'id': 37},
    ]
    return repository_mock


@pytest.fixture
def ldmf_country_handler(mock_ldmf_countries_repository):
    return LDMFCountryHandler(ldmf_countries_repository=mock_ldmf_countries_repository)


@pytest.fixture
def outcomes_handler():
    return OutcomesHandler()


@pytest.mark.asyncio
class TestClientNameHandler:
    async def test_client_name_already_confirmed(self, client_name_handler):
        confirmed_data = ConfirmedData(client_name='Confirmed Client')
        aggregated_data = AggregatedData()
        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_multiple_client_names_in_aggregated_data_found_in_api(
        self, client_name_handler, mock_quals_clients_repository
    ):
        aggregated_data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return a single client for each search request
        mock_quals_clients_repository.search_clients.side_effect = [
            ClientSearchResponse(
                clients=[ClientSearchItem(id='1', name='Client A', qualsCount=1, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
            ClientSearchResponse(
                clients=[ClientSearchItem(id='2', name='Client B', qualsCount=1, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
            ),
        ]

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.PENDING_CONFIRMATION
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A', 'Client B']
        mock_quals_clients_repository.search_clients.assert_has_calls(
            [
                call(ClientSearchRequest(contains='Client A', page_size=5, page_idx=0)),
                call(ClientSearchRequest(contains='Client B', page_size=5, page_idx=0)),
            ],
            any_order=True,
        )

    async def test_multiple_client_names_in_aggregated_data_api_fails(
        self, client_name_handler, mock_quals_clients_repository
    ):
        aggregated_data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to raise an exception
        mock_quals_clients_repository.search_clients.side_effect = Exception('API Error')

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_MULTIPLE_OPTIONS
        assert response.options == ['Client A', 'Client B']

    async def test_single_client_name_api_multiple_matches(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Single Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return multiple clients
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[
                ClientSearchItem(id='3', name='Single Client 1', qualsCount=1, clientConfidentiality=1),
                ClientSearchItem(id='4', name='Single Client 2', qualsCount=1, clientConfidentiality=1),
            ],
            total_count=2,
            page_size=5,
            page_idx=0,
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MULTIPLE
        assert response.system_message == CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Single Client')
        assert response.options == ['Single Client']
        mock_quals_clients_repository.search_clients.assert_called_once_with(
            ClientSearchRequest(contains='Single Client', page_size=5, page_idx=0)
        )

    async def test_single_client_name_api_no_matches(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['NonExistent Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return no clients
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[], total_count=0, page_size=5, page_idx=0
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == CLIENT_NOT_FOUND_PROMPT.format(client_name='NonExistent Client')
        assert response.options == ['NonExistent Client']

    async def test_single_client_name_api_exact_match(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Exact Match Client'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to return exactly one client
        mock_quals_clients_repository.search_clients.return_value = ClientSearchResponse(
            clients=[ClientSearchItem(id='5', name='Exact Match Client', qualsCount=1, clientConfidentiality=1)],
            total_count=1,
            page_size=5,
            page_idx=0,
        )

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.options == []

    async def test_single_client_name_api_fails(self, client_name_handler, mock_quals_clients_repository):
        aggregated_data = AggregatedData(client_name=['Client With Error'])
        confirmed_data = ConfirmedData()

        # Mock search_clients to raise an exception
        mock_quals_clients_repository.search_clients.side_effect = Exception('API Error')

        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name='Client With Error')
        assert response.options == ['Client With Error']

    async def test_no_client_names_in_aggregated_data(self, client_name_handler):
        aggregated_data = AggregatedData(client_name=[])
        confirmed_data = ConfirmedData()
        response = await client_name_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_CLIENT_NAME
        assert response.next_expected_field is None


@pytest.mark.asyncio
class TestLDMFCountryHandler:
    async def test_ldmf_country_handler_returns_missing(self, ldmf_country_handler):
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_ldmf_country_already_confirmed(self, ldmf_country_handler):
        confirmed_data = ConfirmedData(ldmf_country='Germany')
        aggregated_data = AggregatedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_single_valid_country(self, ldmf_country_handler):
        """Test handling of a single valid country."""
        aggregated_data = AggregatedData(ldmf_country=['Germany'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_single_invalid_country(self, ldmf_country_handler):
        """Test handling of a single invalid country."""
        aggregated_data = AggregatedData(ldmf_country=['InvalidCountry'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_multiple_valid_countries(self, ldmf_country_handler):
        """Test handling of multiple valid countries."""
        aggregated_data = AggregatedData(ldmf_country=['Germany', 'United States'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_multiple_mixed_countries(self, ldmf_country_handler):
        """Test handling of mixed valid and invalid countries."""
        aggregated_data = AggregatedData(ldmf_country=['Germany', 'InvalidCountry', 'United States'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None

    async def test_multiple_invalid_countries(self, ldmf_country_handler):
        """Test handling of multiple invalid countries."""
        aggregated_data = AggregatedData(ldmf_country=['InvalidCountry1', 'InvalidCountry2'])
        confirmed_data = ConfirmedData()
        response = await ldmf_country_handler.check_and_get_response(
            aggregated_data, confirmed_data, token='test_token'
        )
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_LDMF_COUNTRY
        assert response.next_expected_field is None


@pytest.mark.asyncio
class TestOutcomesHandler:
    async def test_outcomes_already_confirmed(self, outcomes_handler):
        confirmed_data = ConfirmedData(outcomes='Confirmed Outcome')
        aggregated_data = AggregatedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is False
        assert response.field_status == FieldStatus.CONFIRMED
        assert response.system_message is None
        assert response.next_expected_field is None

    async def test_outcomes_in_aggregated_data(self, outcomes_handler):
        aggregated_data = AggregatedData(outcomes='Aggregated Outcome')
        confirmed_data = ConfirmedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == OUTCOMES_AGGREGATED_QUESTION.format(outcomes=aggregated_data.outcomes)
        assert response.next_expected_field is None

    async def test_outcomes_missing(self, outcomes_handler):
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        response = await outcomes_handler.check_and_get_response(aggregated_data, confirmed_data)
        assert response.needs_confirmation is True
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_OUTCOMES
        assert response.next_expected_field is None
