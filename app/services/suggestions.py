from dataclasses import dataclass
import logging
from uuid import UUID

from fastapi import UploadFile

from constants.message import (
    ConversationMessageIntention,
    SuggestedUserPrompt,
)
from schemas.extracted_data import AggregatedData
from services.kx_dash import KXDashService


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class SuggestedPromptsGenerator:
    """Detector for conversation message suggested prompts."""

    conversation_id: UUID
    user_message: str
    intention: ConversationMessageIntention
    kx_dash_service: KXDashService
    aggregated_data: AggregatedData
    auth_token: str
    files: list[UploadFile] | None = None

    async def run(self) -> list[SuggestedUserPrompt]:
        """Run the suggested prompts generator."""

        user_provide_input_or_client = await self._user_provide_input_or_client()
        user_provide_input_or_ldmf_country = await self._user_provide_input_or_ldmf_country()
        user_selects_brief_description = await self._user_selects_brief_description()

        user_has_dash_tasks = await self._user_has_dash_tasks()
        user_navigates_from_dash_task = await self._user_navigates_from_dash_task()
        user_provided_all_required_fields = await self._user_provided_all_required_fields()

        ai_detected_multiple_client_name_variations = await self._ai_detected_multiple_client_name_variations()
        ai_retrieves_single_client_name = await self._ai_retrieves_single_client_name()
        ai_couldnt_find_provided_client_name = await self._ai_couldnt_find_provided_client_name()
        ai_retrieves_single_ldmf_country = await self._ai_retrieves_single_ldmf_country()
        ai_detected_multiple_ldmf_country_variations = await self._ai_detected_multiple_ldmf_country_variations()

        suggested_prompts = []

        ##################
        # USE CASE 1, 2
        if user_has_dash_tasks and user_navigates_from_dash_task:
            """
            1. GIVEN the user has assigned one or more than one dash task(s)
            2. AND the user navigates from the Dash task in the KX Dash app/KX Dash actionable email/Home page
            3. WHEN the user navigates to the Prompt page
            4. VERIFY that the user can see the "No, create a new qual" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_NEW_QUAL)

        ##################
        # USE CASE 3, 4, 5
        if user_selects_brief_description:
            """
            1. GIVEN the user is on the Prompt page
            2. WHEN the user selects the option "Write a brief description"
            3. VERIFY that the user can see the "Show me an example prompt" as a clickable option under the message
            """
            suggested_prompts.append(SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT)

        ##################
        # USE CASE 6, 7
        if user_provided_all_required_fields:
            """
            1. GIVEN the user is on the Prompt page
            2. WHEN the user provides relevant info for all required fields to generate the qual
            3. VERIFY that the user can see the "No, create my qual" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_MY_QUAL)

        ##################
        # USE CASE 8, 9
        if user_provide_input_or_client and ai_detected_multiple_client_name_variations:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI detects a few variations of the Client name
            4. VERIFY that the user can see the "Enter a new client" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)

        ##################
        # USE CASE 10, 11
        if user_provide_input_or_client and ai_retrieves_single_client_name:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI retrieves a single result of the client's name
            4. VERIFY that the user can see the "Yes, this is correct" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.YES_THIS_IS_CORRECT)

        ##################
        # USE CASE 12, 13
        if (
            user_provide_input_or_client
            and (ai_retrieves_single_client_name or ai_couldnt_find_provided_client_name)
            and self.user_message.strip() != SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME.value
        ):
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI retrieves a single result of the client's name
            4. OR AI couldn't find the provided client name in the system
            5. VERIFY that the user can see the "No, I'll enter the client name" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)

        ##################
        # USE CASE 14, 15
        if user_provide_input_or_client and ai_couldnt_find_provided_client_name:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI couldn't find the provided client name in the system (unique)
            4. VERIFY that the user can see the "Yes" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.YES)

        ##################
        # USE CASE 16, 17
        if user_provide_input_or_ldmf_country and (
            ai_retrieves_single_ldmf_country or ai_detected_multiple_ldmf_country_variations
        ):
            """
            1. GIVEN the user navigates to the Prompt page
            2. WHEN the user provides a prompt/uploads a document containing one
            OR several possible Lead Deloitte Member Firm values
            3. VERIFY that the user can see the "No, I'll enter the Lead Deloitte Member Firm" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM)

        return suggested_prompts

    ############################################################
    # utils

    async def _user_provide_input_or_client(self) -> bool:
        """Check if user provides input (message/files) or if client data exists."""
        return bool(self.user_message.strip()) or bool(self.files) or bool(self.aggregated_data.client_name)

    async def _user_provide_input_or_ldmf_country(self) -> bool:
        """Check if user provides input (message/files) or if LDMF firm data exists."""
        return bool(self.user_message.strip()) or bool(self.files) or bool(self.aggregated_data.ldmf_country)

    async def _user_selects_brief_description(self) -> bool:
        """Check if user selects brief description."""
        return self.intention == ConversationMessageIntention.UNCERTAINTY

    async def _user_has_dash_tasks(self) -> bool:
        """Check if user has dash tasks."""
        # TODO: check this method after task #2325786 is completed. Some filtering on activity_id might be needed, but thats not certain yet.
        dash_tasks = await self.kx_dash_service.list(self.auth_token)
        return bool(dash_tasks)

    async def _user_navigates_from_dash_task(self) -> bool:
        """Check if user navigates from dash task."""
        # TODO: fix after task #2325786
        # [BE][Qual-Gen AI platform (Prompt page)] KX Dash: handle creation of conversation from a Dash task
        # https://deloittebsg.visualstudio.com/KM%20and%20Collaboration-KX%20Quals/_workitems/edit/2325786

        # Main way of detecting if user navigates from a dash task is to get activity_id from the conversation creation request.

        # Currently we dont have a way of detecting if user navigates from a dash task,
        # but there will be activity_id field passed during conversation creation,
        # and it can be used to do on_select with this dash task and call method in kxdash service
        return False

    async def _user_provided_all_required_fields(self) -> bool:
        """Check if all required fields are filled for qual generation."""
        aggregated_data = self.aggregated_data
        return all(
            [
                bool(aggregated_data.client_name),
                bool(aggregated_data.ldmf_country),
                bool(aggregated_data.date_intervals),
                bool(aggregated_data.objective_and_scope),
                bool(aggregated_data.outcomes),
            ]
        )

    async def _ai_detected_multiple_client_name_variations(self) -> bool:
        """Check if multiple client name variations were detected."""
        return len(self.aggregated_data.client_name) > 1

    async def _ai_detected_multiple_ldmf_country_variations(self) -> bool:
        """Check if multiple LDMF country variations were detected."""
        return len(self.aggregated_data.ldmf_country) > 1

    async def _ai_retrieves_single_client_name(self) -> bool:
        """Check if a single client name was retrieved."""
        return len(self.aggregated_data.client_name) == 1

    async def _ai_retrieves_single_ldmf_country(self) -> bool:
        """Check if a single LDMF country was retrieved."""
        return len(self.aggregated_data.ldmf_country) == 1

    async def _ai_couldnt_find_provided_client_name(self) -> bool:
        """Check if no client name was found in the extracted data."""
        return len(self.aggregated_data.client_name) == 0

    async def _ai_couldnt_find_ldmf_country_variations(self) -> bool:
        """Check if no LDMF country was found in the extracted data."""
        return len(self.aggregated_data.ldmf_country) == 0
