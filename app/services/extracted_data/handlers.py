from abc import ABC, abstractmethod
import asyncio
import logging

from constants.extracted_data import Field<PERSON>tat<PERSON>, RequiredField
from constants.message import (
    CLIENT_NAME_MULTIPLE_OPTIONS,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    LDMF_COUNTRY_MULTIPLE_OPTIONS,
    LDMF_COUNTRY_SINGLE_CONFIRMATION,
    NEED_INFO_CLIENT_NAME,
    NEED_INFO_LDMF_COUNTRY,
    NEED_INFO_OBJECTIVE_SCOPE,
    NEED_INFO_OUTCOMES,
    OUTCOMES_AGGREGATED_QUESTION,
)
from repositories import LDMFCountriesRepository, QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, ClientSearchResponse, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


logger = logging.getLogger(__name__)


__all__ = [
    '<PERSON><PERSON><PERSON><PERSON>Handler',
    '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    'DateIntervalsHandler',
    '<PERSON>bject<PERSON><PERSON><PERSON><PERSON>',
    'OutcomesHandler',
    '<PERSON><PERSON>ieldHandler',
    'TokenRequiredFieldHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass  # pragma: no cover


class TokenRequiredFieldHandler(ABC):
    """
    Abstract base class for handlers that require external API authentication.
    This interface is for handlers that need a token for external API calls.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            token: MSAL token from the user for API authentication

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass  # pragma: no cover


class ClientNameHandler(BaseFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    def __init__(self, quals_clients_repository: QualsClientsRepository):
        self.quals_clients_repository = quals_clients_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if client name is already confirmed
        if confirmed_data.client_name is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if we have multiple client names in aggregated data
        if len(aggregated_data.client_name) > 1:
            # Search for each client name using the API
            try:
                # Create search requests for each client name
                search_requests = [
                    ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
                    for client_name in aggregated_data.client_name
                ]

                # Perform concurrent searches
                search_results: list[ClientSearchResponse | BaseException] = await asyncio.gather(
                    *[self.quals_clients_repository.search_clients(request) for request in search_requests],
                    return_exceptions=True,
                )

                # Collect found client names
                found_client_names = []
                failed_searches = []
                for i, result in enumerate(search_results):
                    if isinstance(result, Exception):
                        failed_searches.append((aggregated_data.client_name[i], result))
                    else:
                        clients = [client.name for client in result.clients]  # type: ignore
                        found_client_names.extend(clients)

                if failed_searches:
                    raise Exception(failed_searches[0][0])

                if found_client_names:
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.PENDING_CONFIRMATION,
                        system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                        next_expected_field=None,
                        options=found_client_names,
                    )
            except Exception:
                # If API search fails, fall back to showing all options
                pass

            # Fallback: show all client names as options
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=aggregated_data.client_name,
            )

        # Check if we have a single client name
        elif len(aggregated_data.client_name) == 1:
            client_name = aggregated_data.client_name[0]

            # Search for the single client name using the API
            try:
                search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
                search_result = await self.quals_clients_repository.search_clients(search_request)

                # If exactly ONE confident match is found, automatically proceed (no user confirmation needed)
                if search_result.clients and len(search_result.clients) == 1:
                    # Auto-confirm the client name - return as confirmed
                    # Note: The auto-confirmation logic will be handled in the service layer
                    return FieldHandlerResponse(
                        needs_confirmation=False,
                        field_status=FieldStatus.CONFIRMED,
                        system_message=None,
                        next_expected_field=None,
                        options=[],  # No further selection required
                    )

                # If multiple matches or no matches, ask for confirmation
                elif search_result.clients and len(search_result.clients) > 1:
                    # Multiple matches found - use the original client name but ask for confirmation
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.MULTIPLE,
                        system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                        next_expected_field=None,
                        options=[client_name],
                    )
                else:
                    # No matches found - ask if user wants to add as new client
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.SINGLE,
                        system_message=CLIENT_NOT_FOUND_PROMPT.format(client_name=client_name),
                        next_expected_field=None,
                        options=[client_name],
                    )

            except Exception:
                # If API search fails, fall back to asking for confirmation
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                    next_expected_field=None,
                    options=[client_name],
                )

        # No client names found
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_CLIENT_NAME,
                next_expected_field=None,
            )


class LDMFCountryHandler(TokenRequiredFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    def __init__(self, ldmf_countries_repository: LDMFCountriesRepository):
        self.ldmf_countries_repository = ldmf_countries_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        if confirmed_data.ldmf_country is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        try:
            all_countries = await self.ldmf_countries_repository.get_ldmf_countries(token)
            all_countries_names = {item.name.lower() for item in all_countries}
        except Exception as e:
            logger.error('Error getting LDMF countries: %s', e)
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_LDMF_COUNTRY,
                next_expected_field=None,
            )
        if aggregated_data.ldmf_country is None or len(aggregated_data.ldmf_country) == 0:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_LDMF_COUNTRY,
                next_expected_field=None,
            )

        elif len(aggregated_data.ldmf_country) > 1:
            confirmed_countries = [item for item in aggregated_data.ldmf_country if item.lower() in all_countries_names]
            if confirmed_countries:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.PENDING_CONFIRMATION,
                    system_message=LDMF_COUNTRY_MULTIPLE_OPTIONS,
                    next_expected_field=None,
                    options=confirmed_countries,
                )
            else:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.MULTIPLE,
                    system_message=LDMF_COUNTRY_MULTIPLE_OPTIONS,
                    next_expected_field=None,
                    options=aggregated_data.ldmf_country,
                )
        elif len(aggregated_data.ldmf_country) == 1:
            ldmf_country = aggregated_data.ldmf_country[0]
            # If exactly ONE confident match is found, automatically proceed (no user confirmation needed)
            if ldmf_country.lower() in all_countries_names:
                return FieldHandlerResponse(
                    needs_confirmation=False,
                    field_status=FieldStatus.CONFIRMED,
                    system_message=None,
                    next_expected_field=None,
                    options=[ldmf_country],
                )
            else:
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=LDMF_COUNTRY_SINGLE_CONFIRMATION.format(ldmf_country=ldmf_country),
                    next_expected_field=None,
                    options=[ldmf_country],
                )
        # No ldmf country found
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=NEED_INFO_LDMF_COUNTRY,
            next_expected_field=None,
        )


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement date intervals handler',
            next_expected_field=None,
        )


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if objective_and_scope is already confirmed
        if confirmed_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if objective_and_scope is in aggregated data
        elif aggregated_data.objective_and_scope is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f'Found this objective and scope: {aggregated_data.objective_and_scope}. Can you confirm?',
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OBJECTIVE_SCOPE,
                next_expected_field=RequiredField.OBJECTIVE_SCOPE,
            )


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=OUTCOMES_AGGREGATED_QUESTION.format(outcomes=aggregated_data.outcomes),
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OUTCOMES,
                next_expected_field=None,
            )
