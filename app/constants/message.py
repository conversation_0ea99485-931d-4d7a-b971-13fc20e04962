import json
import logging
from pathlib import Path

from core.enum import StrEnum


__all__ = [
    'MessageRole',
    'MessageType',
    'OptionType',
    'WELCOME_MESSAGE',
    'ConversationMessageIntention',
    'SuggestedUserPrompt',
    'DASH_TASK_SELECTED_TEMPLATE',
    'INTENT_OBJECT_STRUCTURE',
    'GENERAL_INTENTIONS',
    'BRIEF_DESCRIPTION_REPLY',
    'EXAMPLE_REPLY',
    'EXTRACT_DATA_SYSTEM_PROMPT',
    'EXTRACT_DATA_USER_PROMPT',
    'NEED_INFO_OUTCOMES',
    'NEED_INFO_OBJECTIVE_SCOPE',
    'DASH_TASK_SELECTED_TEMPLATE',
    'WELCOME_MESSAGE_WITH_ONE_DASH_TASK',
    'WELCOME_MESSAGE_WITH_MANY_DASH_TASKS',
    'CLIENT_NAME_MULTIPLE_OPTIONS',
    'CLIENT_NAME_SINGLE_CONFIRMATION',
    'CLIENT_NAME_CONFIRMED',
    'CLIENT_NOT_FOUND_PROMPT',
    'CLIENT_CREATION_CONFIRMED',
    'NEED_INFO_CLIENT_NAME',
    'LDMF_COUNTRY_CONFIRMED',
    'LDMF_COUNTRY_MULTIPLE_OPTIONS',
    'LDMF_COUNTRY_SINGLE_CONFIRMATION',
    'NEED_INFO_LDMF_COUNTRY',
    'NEED_INFO_INITIAL_PROMPT',
    'OBJECTIVE_AND_SCOPE_CONFIRMED',
    'OUTCOMES_CONFIRMED',
    'DATES_CONFIRMED',
    'DATES_AMBIGUOUS',
    'DATES_ONE_DATE',
    'EXTRACT_DATES_SYSTEM_PROMPT',
    'EXTRACT_DATES_USER_PROMPT',
    'ADDITIONAL_DATA_REPLY',
    'READY_TO_GENERATE_QUAL_REPLY',
    'CONFIRMED_FIELDS_READY',
    'READY_TO_CREATE_DRAFT_QUAL',
    'OUTCOMES_AGGREGATED_QUESTION',
]


logger = logging.getLogger(__name__)


class MessageRole(StrEnum):
    SYSTEM = 'system'
    USER = 'user'


class MessageType(StrEnum):
    TEXT = 'text'
    FILE = 'file'
    FORM = 'form'
    TEXT_WITH_FILE = 'text_with_file'


def _load_file_from_folder(folder: str, file_name: str) -> str:
    path = Path(__file__).parent.parent / 'assets' / folder / file_name
    return path.read_text(encoding='utf-8').strip()


WELCOME_MESSAGE = _load_file_from_folder('conversation_messages', 'welcome_message.txt')
DASH_TASK_SELECTED_TEMPLATE = _load_file_from_folder('conversation_messages', 'dash_task_selected.txt')
INTENT_OBJECT_STRUCTURE = _load_file_from_folder('intentions', 'intent_object_structure.txt')
GENERAL_INTENTIONS = json.loads(_load_file_from_folder('intentions', 'general_intentions.json'))
BRIEF_DESCRIPTION_REPLY = _load_file_from_folder('conversation_messages', 'brief_description_reply.txt')
UNDEFINED_REPLY = _load_file_from_folder('conversation_messages', 'undefined_reply.txt')
EXAMPLE_REPLY = _load_file_from_folder('conversation_messages', 'example_reply.txt')
EXTRACT_DATA_SYSTEM_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_data_system_prompt.txt')
EXTRACT_DATA_USER_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_data_user_prompt.txt')

NEED_INFO_OUTCOMES = _load_file_from_folder('conversation_messages', 'need_info_outcomes.txt')
NEED_INFO_OBJECTIVE_SCOPE = _load_file_from_folder('conversation_messages', 'need_info_objective_scope.txt')
WELCOME_MESSAGE = _load_file_from_folder('conversation_messages', 'welcome_message.txt')
DASH_TASK_SELECTED_TEMPLATE = _load_file_from_folder('conversation_messages', 'dash_task_selected.txt')
DASH_TASK_DISCARD = _load_file_from_folder('conversation_messages', 'dash_task_discard.txt')
WELCOME_MESSAGE_WITH_ONE_DASH_TASK = _load_file_from_folder(
    'conversation_messages', 'welcome_message_with_one_dash_task.txt'
)
WELCOME_MESSAGE_WITH_MANY_DASH_TASKS = _load_file_from_folder(
    'conversation_messages', 'welcome_message_with_many_dash_tasks.txt'
)

CLIENT_NAME_MULTIPLE_OPTIONS = _load_file_from_folder('conversation_messages', 'client_name_multiple_options.txt')
CLIENT_NAME_SINGLE_CONFIRMATION = _load_file_from_folder('conversation_messages', 'client_name_single_confirmation.txt')
CLIENT_NAME_CONFIRMED = _load_file_from_folder('conversation_messages', 'client_name_confirmed.txt')
CLIENT_NOT_FOUND_PROMPT = _load_file_from_folder('conversation_messages', 'client_not_found_prompt.txt')
CLIENT_CREATION_CONFIRMED = _load_file_from_folder('conversation_messages', 'client_creation_confirmed.txt')

DATES_CONFIRMED = _load_file_from_folder('conversation_messages', 'dates_confirmed.txt')
DATES_AMBIGUOUS = _load_file_from_folder('conversation_messages', 'dates_ambiguous.txt')
DATES_ONE_DATE = _load_file_from_folder('conversation_messages', 'dates_one_date.txt')

EXTRACT_DATES_SYSTEM_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_dates_system_prompt.txt')
EXTRACT_DATES_USER_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_dates_user_prompt.txt')

NEED_INFO_CLIENT_NAME = 'I need the client name to create the qual. Can you provide the client name?'  # it's out of scope of this task, but should provide something to pass tests

LDMF_COUNTRY_MULTIPLE_OPTIONS = _load_file_from_folder('conversation_messages', 'ldmf_country_multiple_options.txt')
LDMF_COUNTRY_SINGLE_CONFIRMATION = _load_file_from_folder(
    'conversation_messages', 'ldmf_country_single_confirmation.txt'
)
LDMF_COUNTRY_CONFIRMED = _load_file_from_folder('conversation_messages', 'ldmf_country_confirmed.txt')
NEED_INFO_LDMF_COUNTRY = 'I need the LDMF country to create the qual. Can you provide the LDMF country?'
NEED_INFO_INITIAL_PROMPT = "I'll need some more details in order to create a draft qual. Could you start by telling me about the client and what services we delivered?"
OBJECTIVE_AND_SCOPE_CONFIRMED = _load_file_from_folder('conversation_messages', 'objective_and_scope_confirmed.txt')
OUTCOMES_CONFIRMED = _load_file_from_folder('conversation_messages', 'outcomes_confirmed.txt')
OUTCOMES_AGGREGATED_QUESTION = 'Found these outcomes: {outcomes} can you confirm?'

CONFIRMED_FIELDS_READY = 'Got it! Anything else to add?'
READY_TO_CREATE_DRAFT_QUAL = _load_file_from_folder('conversation_messages', 'ready_to_create_draft_qual.txt')

ADDITIONAL_DATA_REPLY = _load_file_from_folder('conversation_messages', 'additional_data_reply.txt')
READY_TO_GENERATE_QUAL_REPLY = _load_file_from_folder('conversation_messages', 'ready_to_generate_qual_reply.txt')


class OptionType(StrEnum):
    DATES = 'dates'
    LDMF_COUNTRY = 'ldmf_country'
    CLIENT_NAME = 'client_name'
    KX_DASH_TASK = 'kx_dash_task'


class ConversationMessageIntention(StrEnum):
    UNDEFINED = 'undefined'

    GENERATE_QUAL = 'generate_qual'
    EXTRACTION = 'extraction'
    EXAMPLE = 'example'
    DASH_DISCARD = 'dash_discard'
    UNCERTAINTY = 'uncertainty'  # NOTE: used to be BRIEF_DESCRIPTION, renamed for better LLM understanding
    NEED_CONTEXT = 'need_context'  # NOTE: mapped to UNCERTAINTY
    USER_CONFIRMATION = 'user_confirmation'


class SuggestedUserPrompt(StrEnum):
    """Suggested user replies for the conversation."""

    NO_CREATE_NEW_QUAL = 'No, create a new qual'
    SHOW_ME_AN_EXAMPLE_PROMPT = 'Show me an example prompt'
    NO_CREATE_MY_QUAL = 'No, create my qual'
    ENTER_A_NEW_CLIENT = 'Enter a new client'
    YES_THIS_IS_CORRECT = 'Yes, this is correct'
    NO_I_WILL_ENTER_CLIENT_NAME = "No, I'll enter the client name"
    YES = 'Yes'
    YES_ADD_NEW_CLIENT = 'Yes, add a new client'
    NO_ENTER_DIFFERENT_NAME = 'No, enter different name'
    NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM = "No, I'll enter the Lead Deloitte Member Firm"

    # Welcome message suggested prompts
    WRITE_A_BRIEF_DESCRIPTION = 'Write a brief description'
    UPLOAD_DOCUMENT = 'Upload a document'
