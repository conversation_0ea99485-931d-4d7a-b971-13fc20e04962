<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">67%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html#t25">app/api/auth.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html#t25"><data value='create_signal_r_jwt'>create_signal_r_jwt</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html">app/api/auth.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t31">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t31"><data value='create_conversation_with_welcome_message'>create_conversation_with_welcome_message</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t63">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t63"><data value='get_conversation_by_id'>get_conversation_by_id</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>5</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t94">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t94"><data value='list_messages'>list_messages</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t124">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t124"><data value='delete_conversation'>delete_conversation</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t156">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html#t156"><data value='get_last_message'>get_last_message</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html#t25">app/api/conversation_debug.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html#t25"><data value='get_conversation_by_id_for_debug'>get_conversation_by_id_for_debug</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html">app/api/conversation_debug.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html#t23">app/api/kx_dash.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html#t23"><data value='list_activities'>list_activities</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html#t51">app/api/kx_dash.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html#t51"><data value='get_activity'>get_activity</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html">app/api/kx_dash.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html#t25">app/api/message.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html#t25"><data value='create'>create</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>3</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html#t86">app/api/message.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html#t86"><data value='get'>get</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>5</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html">app/api/message.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html#t14">app/api/root.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html#t14"><data value='health'>health</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html">app/api/root.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t61">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t61"><data value='uri'>DatabaseSettings.uri</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t88">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t88"><data value='supported_extensions'>BlobStorageSettings.supported_extensions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>104</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="103 104">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html#t10">app/config/logging.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html#t10"><data value='configure_logging'>configure_logging</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html">app/config/logging.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html#t9">app/config/openapi.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html#t9"><data value='setup_custom_openapi'>setup_custom_openapi</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html#t12">app/config/openapi.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html#t12"><data value='custom_openapi'>setup_custom_openapi.custom_openapi</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html">app/config/openapi.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html">app/constants/auth.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html">app/constants/environment.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t66">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t66"><data value='load_file_from_folder'>_load_file_from_folder</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>78</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="78 78">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_db_py.html">app/core/db.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_db_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t10">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t10"><data value='all'>CustomEnum.all</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t14">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t14"><data value='values'>CustomEnum.values</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t17">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t17"><data value='hash__'>CustomEnum.__hash__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t22">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t22"><data value='str__'>StrEnum.__str__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t27">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t27"><data value='int__'>IntEnum.__int__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html#t12">app/core/json.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html#t12"><data value='default'>CustomJSONEncoder.default</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html">app/core/json.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html#t23">app/core/schemas.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html#t23"><data value='serialize_date'>CustomModel.serialize_date</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html">app/core/schemas.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t11">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t11"><data value='url_join'>url_join</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t16">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t16"><data value='init__'>URLResolver.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t20">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t20"><data value='build_routes_map'>URLResolver._build_routes_map</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t27">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t27"><data value='reverse'>URLResolver.reverse</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7___init___py.html">app/core/validators/__init__.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html#t4">app/core/validators/document_files.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html#t4"><data value='validate_files_core'>validate_files_core</data></a></td>
                <td>22</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="3 22">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html">app/core/validators/document_files.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html#t7">app/core/validators/suggested_prompts.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html#t7"><data value='validate_value_as_list_of_strings'>validate_value_as_list_of_strings</data></a></td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html">app/core/validators/suggested_prompts.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14___init___py.html">app/dependencies/__init__.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html#t25">app/dependencies/db.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html#t25"><data value='get_db'>get_db</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html">app/dependencies/db.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t17">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t17"><data value='log_giveup'>_log_giveup</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t28">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t28"><data value='should_givup'>_should_givup</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t66">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t66"><data value='init__'>CustomAsyncClient.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t71">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t71"><data value='request'>CustomAsyncClient.request</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t78">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t78"><data value='get_http_client'>get_http_client</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t28">app/dependencies/permissions.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t28"><data value='call__'>OwnerOnlyPermission.__call__</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t47">app/dependencies/permissions.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t47"><data value='get_permission_check_method_data'>OwnerOnlyPermission._get_permission_check_method_data</data></a></td>
                <td>23</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="13 23">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html">app/dependencies/permissions.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t42">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t42"><data value='get_openai_repository'>get_openai_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t50">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t50"><data value='get_conversation_repository'>get_conversation_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t57">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t57"><data value='get_conversation_message_repository'>get_conversation_message_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t68">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t68"><data value='get_document_db_repository'>get_document_db_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t75">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t75"><data value='get_document_blob_repository'>get_document_blob_repository</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t84">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t84"><data value='get_kx_dash_repository'>get_kx_dash_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t91">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t91"><data value='get_quals_clients_repository'>get_quals_clients_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t98">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t98"><data value='get_ldmf_countries_repository'>get_ldmf_countries_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t105">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t105"><data value='get_extracted_data_repository'>get_extracted_data_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t114">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t114"><data value='get_document_queue_repository'>get_document_queue_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t124">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t124"><data value='get_industry_repository'>get_industry_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t131">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t131"><data value='get_role_repository'>get_role_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t138">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html#t138"><data value='get_service_repository'>get_service_repository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t47">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t47"><data value='get_auth_service'>get_auth_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t54">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t54"><data value='get_intent_classifier_service'>get_intent_classifier_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t62">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t62"><data value='get_industry_data_service'>get_industry_data_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t71">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t71"><data value='get_role_data_service'>get_role_data_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t80">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t80"><data value='get_service_data_service'>get_service_data_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t89">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t89"><data value='get_extracted_data_service'>get_extracted_data_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t112">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t112"><data value='get_kx_dash_service'>get_kx_dash_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t125">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t125"><data value='get_document_service'>get_document_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t140">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t140"><data value='get_date_validator_service'>get_date_validator_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t147">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t147"><data value='get_conversation_message_service'>get_conversation_message_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t170">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html#t170"><data value='get_conversation_service'>get_conversation_service</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408___init___py.html">app/exceptions/__init__.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html">app/exceptions/base.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t14">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t14"><data value='init__'>MaximumDocumentsNumberExceeded.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t27">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t27"><data value='init__'>MaximumDocumentsSizeExceeded.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t9">app/exceptions/entity.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t9"><data value='get_errormessage_404'>get_errormessage_404</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t16">app/exceptions/entity.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t16"><data value='init__'>EntityNotFoundError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html">app/exceptions/entity.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html#t10">app/exceptions/kx_dash.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html#t10"><data value='init__'>KXDashDateParsingError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html">app/exceptions/kx_dash.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4___init___py.html">app/middleware/__init__.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t27">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t27"><data value='init__'>InvalidAuthorization.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t53">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t53"><data value='dispatch'>AzureADAuthorizerMiddleware.dispatch</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t74">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t74"><data value='auth_required'>AzureADAuthorizerMiddleware._auth_required</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t86">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t86"><data value='set_auth_free_endpoint_paths'>AzureADAuthorizerMiddleware._set_auth_free_endpoint_paths</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t97">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t97"><data value='validate_token_scopes'>AzureADAuthorizerMiddleware._validate_token_scopes</data></a></td>
                <td>18</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="14 18">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t122">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t122"><data value='decode_token'>AzureADAuthorizerMiddleware._decode_token</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t151">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t151"><data value='get_token_key'>AzureADAuthorizerMiddleware._get_token_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t161">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t161"><data value='get_jwt_keys'>AzureADAuthorizerMiddleware._get_jwt_keys</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t181">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t181"><data value='ensure_b64padding'>AzureADAuthorizerMiddleware._ensure_b64padding</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t187">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t187"><data value='get_user'>AzureADAuthorizerMiddleware._get_user</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html#t12">app/middleware/exc_to_resp.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html#t12"><data value='dispatch'>ExceptionToResponseMiddleware.dispatch</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html">app/middleware/exc_to_resp.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t21">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t21"><data value='get_next_revision_id'>get_next_revision_id</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t54">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t54"><data value='process_revision_directives'>process_revision_directives</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t71">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t71"><data value='get_url'>get_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t76">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t76"><data value='run_migrations_offline'>run_migrations_offline</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t90">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html#t90"><data value='run_migrations_online'>run_migrations_online</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="20 21">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html#t23">app/migrations/versions/0001_create_a_conversation_and_message_models.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html#t23"><data value='upgrade'>upgrade</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html#t57">app/migrations/versions/0001_create_a_conversation_and_message_models.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html#t57"><data value='downgrade'>downgrade</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html">app/migrations/versions/0001_create_a_conversation_and_message_models.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html#t23">app/migrations/versions/0002_add_document_model.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html#t23"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html#t44">app/migrations/versions/0002_add_document_model.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html#t44"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html">app/migrations/versions/0002_add_document_model.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html#t22">app/migrations/versions/0003_message_change.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html#t34">app/migrations/versions/0003_message_change.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html#t34"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html">app/migrations/versions/0003_message_change.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html#t22">app/migrations/versions/0004_add_model_for_extracted_data.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html#t52">app/migrations/versions/0004_add_model_for_extracted_data.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html#t52"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html">app/migrations/versions/0004_add_model_for_extracted_data.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html#t22">app/migrations/versions/0005_new_columns_in_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html#t32">app/migrations/versions/0005_new_columns_in_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html#t32"><data value='downgrade'>downgrade</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html">app/migrations/versions/0005_new_columns_in_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html#t22">app/migrations/versions/0006_qualid_in_conversation_made_nullable.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html#t30">app/migrations/versions/0006_qualid_in_conversation_made_nullable.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html#t30"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html">app/migrations/versions/0006_qualid_in_conversation_made_nullable.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html#t24">app/migrations/versions/0007_add_processing_table.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html#t24"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html#t42">app/migrations/versions/0007_add_processing_table.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html#t42"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html">app/migrations/versions/0007_add_processing_table.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html#t22">app/migrations/versions/0008_add_intention_to_qualconversationmessage.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html#t44">app/migrations/versions/0008_add_intention_to_qualconversationmessage.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html#t44"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html">app/migrations/versions/0008_add_intention_to_qualconversationmessage.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html#t21">app/migrations/versions/0009_index_on_createdat_and_id_on_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html#t21"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html#t27">app/migrations/versions/0009_index_on_createdat_and_id_on_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html#t27"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html">app/migrations/versions/0009_index_on_createdat_and_id_on_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html#t22">app/migrations/versions/0010_add_columns_to_qualextracteddata.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html#t33">app/migrations/versions/0010_add_columns_to_qualextracteddata.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html#t33"><data value='downgrade'>downgrade</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html">app/migrations/versions/0010_add_columns_to_qualextracteddata.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html#t22">app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html#t46">app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html#t46"><data value='downgrade'>downgrade</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html">app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html#t22">app/migrations/versions/0012_add_activity_id_field.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html#t29">app/migrations/versions/0012_add_activity_id_field.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html#t29"><data value='downgrade'>downgrade</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html">app/migrations/versions/0012_add_activity_id_field.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html#t22">app/migrations/versions/0013_add_industries_services_and_roles_.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html#t33">app/migrations/versions/0013_add_industries_services_and_roles_.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html#t33"><data value='downgrade'>downgrade</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html">app/migrations/versions/0013_add_industries_services_and_roles_.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html#t22">app/migrations/versions/0014_add_suggested_prompts.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html#t22"><data value='upgrade'>upgrade</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html#t30">app/migrations/versions/0014_add_suggested_prompts.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html#t30"><data value='downgrade'>downgrade</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html">app/migrations/versions/0014_add_suggested_prompts.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html">app/models/qual_conversation.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html">app/models/qual_conversation_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html">app/models/qual_document.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html">app/models/qual_extracted_data.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t25">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t25"><data value='process_bind_param'>JSONEncodedDict.process_bind_param</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t33">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t33"><data value='process_result_value'>JSONEncodedDict.process_result_value</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f___init___py.html">app/repositories/__init__.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t23">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t23"><data value='init__'>ConversationRepository.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t26">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t26"><data value='create'>ConversationRepository.create</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t50">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t50"><data value='get'>ConversationRepository.get</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t64">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t64"><data value='get_internal_id'>ConversationRepository.get_internal_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t78">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t78"><data value='exists'>ConversationRepository.exists</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t92">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t92"><data value='delete'>ConversationRepository.delete</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t106">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t106"><data value='get_owner_id'>ConversationRepository.get_owner_id</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t120">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t120"><data value='update_state'>ConversationRepository.update_state</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t135">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t135"><data value='update_confirmed_data_and_state'>ConversationRepository.update_confirmed_data_and_state</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t157">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t157"><data value='get_confirmed_data'>ConversationRepository.get_confirmed_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t172">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t172"><data value='get_extended'>ConversationRepository.get_extended</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t22">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t22"><data value='init__'>ConversationMessageRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t26">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t26"><data value='create'>ConversationMessageRepository.create</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t51">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t51"><data value='get'>ConversationMessageRepository.get</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t80">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t80"><data value='list'>ConversationMessageRepository.list</data></a></td>
                <td>11</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="2 11">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t113">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t113"><data value='get_last'>ConversationMessageRepository.get_last</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t144">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t144"><data value='delete_many'>ConversationMessageRepository.delete_many</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t164">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t164"><data value='update_fields'>ConversationMessageRepository.update_fields</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t193">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t193"><data value='get_serialized_message'>ConversationMessageRepository._get_serialized_message</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t198">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t198"><data value='get_owner_id'>ConversationMessageRepository.get_owner_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t19">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t19"><data value='init__'>DocumentBlobRepository.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t24">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t24"><data value='initialize'>DocumentBlobRepository.initialize</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t40">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t40"><data value='upload_file'>DocumentBlobRepository.upload_file</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>3</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t76">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t76"><data value='get_service_client'>DocumentBlobRepository._get_service_client</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t80">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t80"><data value='delete_many'>DocumentBlobRepository.delete_many</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>3</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t17">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t17"><data value='init__'>DocumentDbRepository.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t20">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t20"><data value='create'>DocumentDbRepository.create</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t63">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t63"><data value='count_documents_by_conversation_id'>DocumentDbRepository.count_documents_by_conversation_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t88">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t88"><data value='get_total_size_by_conversation_id'>DocumentDbRepository.get_total_size_by_conversation_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t113">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t113"><data value='get_file_info_for_deletion'>DocumentDbRepository.get_file_info_for_deletion</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t138">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t138"><data value='delete_many'>DocumentDbRepository.delete_many</data></a></td>
                <td>10</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="4 10">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t19">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t19"><data value='init__'>DocumentQueueRepository.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t24">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t24"><data value='create_queue_client'>DocumentQueueRepository._create_queue_client</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t32">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t32"><data value='ensure_queue_exists'>DocumentQueueRepository._ensure_queue_exists</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t39">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t39"><data value='send_message'>DocumentQueueRepository._send_message</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t55">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t55"><data value='send_document_message'>DocumentQueueRepository.send_document_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t59">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t59"><data value='send_prompt_message'>DocumentQueueRepository.send_prompt_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t63">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t63"><data value='send_message'>DocumentQueueRepository.send_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t22">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t22"><data value='init__'>ExtractedDataRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t26">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t26"><data value='create'>ExtractedDataRepository.create</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t48">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t48"><data value='get'>ExtractedDataRepository.get</data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t77">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t77"><data value='list'>ExtractedDataRepository.list</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t107">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t107"><data value='update'>ExtractedDataRepository.update</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t135">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t135"><data value='delete'>ExtractedDataRepository.delete</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t157">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t157"><data value='delete_many'>ExtractedDataRepository.delete_many</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t17">app/repositories/industry.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t17"><data value='init__'>IndustryRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t27">app/repositories/industry.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t27"><data value='list'>IndustryRepository.list</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html">app/repositories/industry.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t21">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t21"><data value='init__'>KXDashRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t32">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t32"><data value='mock_list_tasks'>KXDashRepository._mock_list_tasks</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t76">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t76"><data value='list'>KXDashRepository.list</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t92">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t92"><data value='get'>KXDashRepository.get</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t108">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t108"><data value='parse_date_string'>KXDashRepository._parse_date_string</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t130">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t130"><data value='parse_kx_dash_response'>KXDashRepository._parse_kx_dash_response</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t16">app/repositories/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t16"><data value='init__'>LDMFCountriesRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t26">app/repositories/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t26"><data value='get_ldmf_countries'>LDMFCountriesRepository.get_ldmf_countries</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html">app/repositories/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t39">app/repositories/openai.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t39"><data value='init__'>OpenAIRepository.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t44">app/repositories/openai.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t44"><data value='generate_chat_completion'>OpenAIRepository.generate_chat_completion</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>32</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html">app/repositories/openai.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t18">app/repositories/processing_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t18"><data value='init__'>ProcessingMessageRepository.__init__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t21">app/repositories/processing_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t21"><data value='create'>ProcessingMessageRepository.create</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>30</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html">app/repositories/processing_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t26">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t26"><data value='init__'>QualsClientsRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t36">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t36"><data value='generate_mock_search_results'>QualsClientsRepository._generate_mock_search_results</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t73">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t73"><data value='generate_mock_client_comprehensive'>QualsClientsRepository._generate_mock_client_comprehensive</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t95">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t95"><data value='search_clients'>QualsClientsRepository.search_clients</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t148">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t148"><data value='create_client'>QualsClientsRepository.create_client</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t17">app/repositories/role.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t17"><data value='init__'>RoleRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t27">app/repositories/role.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t27"><data value='list'>RoleRepository.list</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html">app/repositories/role.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t17">app/repositories/service.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t17"><data value='init__'>ServiceRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t27">app/repositories/service.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t27"><data value='list'>ServiceRepository.list</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html">app/repositories/service.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html">app/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html">app/schemas/auth.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t20">app/schemas/confirmed_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t20"><data value='from_json_string'>ConfirmedData.from_json_string</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t30">app/schemas/confirmed_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t30"><data value='to_json_string'>ConfirmedData.to_json_string</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html">app/schemas/confirmed_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f___init___py.html">app/schemas/conversation_message/__init__.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t51">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t51"><data value='user_message_is_suggested_reply'>MessageValidator.user_message_is_suggested_reply</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t56">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t56"><data value='validate_suggested_prompts'>MessageValidator.validate_suggested_prompts</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t61">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t61"><data value='validate_files'>MessageValidator.validate_files</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t64">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t64"><data value='model_dump_for_db'>MessageValidator.model_dump_for_db</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t91">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t91"><data value='validate_selected_option'>UserMessageSerializer.validate_selected_option</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t106">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t106"><data value='validate_options'>SystemMessageSerializer.validate_options</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t114">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t114"><data value='validate_suggested_prompts'>SystemMessageSerializer.validate_suggested_prompts</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>65</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="65 65">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t23">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t23"><data value='validate_json_str'>BaseOption.validate_json_str</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html">app/schemas/dash_task.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html#t12">app/schemas/dates.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html#t12"><data value='parse_date_str'>DatesLLMResponse.parse_date_str</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html">app/schemas/dates.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html">app/schemas/document.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t39">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t39"><data value='validate_client_name'>ExtractedData.validate_client_name</data></a></td>
                <td>11</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="2 11">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t55">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t55"><data value='validate_ldmf_country'>ExtractedData.validate_ldmf_country</data></a></td>
                <td>11</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="2 11">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t70">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t70"><data value='create'>ExtractedData.create</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t79">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t79"><data value='model_dump_for_db'>ExtractedData.model_dump_for_db</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html">app/schemas/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html">app/schemas/processing.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html">app/schemas/root.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t26">app/services/auth.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t26"><data value='init__'>AuthService.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t34">app/services/auth.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t34"><data value='generate_signal_r_jwt'>AuthService.generate_signal_r_jwt</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html">app/services/auth.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t15">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t15"><data value='init__'>IndustryDataService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t19">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t19"><data value='set_cached_industries'>IndustryDataService._set_cached_industries</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t22">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t22"><data value='list'>IndustryDataService.list</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t39">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t39"><data value='init__'>ConversationService.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t53">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t53"><data value='create'>ConversationService.create</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>3</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t78">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t78"><data value='create_with_welcome_message'>ConversationService.create_with_welcome_message</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>3</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t106">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t106"><data value='create_welcome_message'>ConversationService._create_welcome_message</data></a></td>
                <td>12</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="5 12">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t157">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t157"><data value='get'>ConversationService.get</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>5</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t184">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t184"><data value='delete'>ConversationService.delete</data></a></td>
                <td>7</td>
                <td>4</td>
                <td>6</td>
                <td class="right" data-ratio="3 7">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t209">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t209"><data value='get_owner_id'>ConversationService.get_owner_id</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t228">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t228"><data value='get_extended'>ConversationService.get_extended</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>5</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t52">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t52"><data value='init__'>ConversationMessageService.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t70">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t70"><data value='create'>ConversationMessageService.create</data></a></td>
                <td>39</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="5 39">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t189">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t189"><data value='create_message'>ConversationMessageService.create_message</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t211">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t211"><data value='get'>ConversationMessageService.get</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>3</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t233">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t233"><data value='list'>ConversationMessageService.list</data></a></td>
                <td>5</td>
                <td>4</td>
                <td>3</td>
                <td class="right" data-ratio="1 5">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t258">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t258"><data value='get_last'>ConversationMessageService.get_last</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t280">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t280"><data value='delete_many'>ConversationMessageService.delete_many</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t301">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t301"><data value='update_message_fields'>ConversationMessageService.update_message_fields</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t316">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t316"><data value='get_system_message_with_result'>ConversationMessageService._get_system_message_with_result</data></a></td>
                <td>10</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="3 10">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t351">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t351"><data value='get_owner_id'>ConversationMessageService.get_owner_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>3</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t369">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t369"><data value='handle_country_selection'>ConversationMessageService._handle_country_selection</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t398">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t398"><data value='handle_dates_selection'>ConversationMessageService._handle_dates_selection</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t453">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t453"><data value='handle_client_name_selection'>ConversationMessageService._handle_client_name_selection</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>3</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t31">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t31"><data value='create_system_message'>DateValidatorService._create_system_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t36">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t36"><data value='create_user_message'>DateValidatorService._create_user_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t40">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t40"><data value='validate_dates'>DateValidatorService.validate_dates</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t22">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t22"><data value='init__'>DocumentService.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t32">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t32"><data value='create_many'>DocumentService.create_many</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>3</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t81">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t81"><data value='delete_many'>DocumentService.delete_many</data></a></td>
                <td>10</td>
                <td>8</td>
                <td>3</td>
                <td class="right" data-ratio="2 10">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t106">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t106"><data value='validate_document_limits'>DocumentService.__validate_document_limits</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t123">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t123"><data value='upload'>DocumentService.__upload</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>3</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t163">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t163"><data value='generate_blob_path'>DocumentService.__generate_blob_path</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t176">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t176"><data value='generate_prompt_blob_path'>DocumentService.__generate_prompt_blob_path</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t188">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t188"><data value='create_prompt'>DocumentService.create_prompt</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187___init___py.html">app/services/extracted_data/__init__.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t42">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t42"><data value='check_and_get_response'>BaseFieldHandler.check_and_get_response</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t64">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t64"><data value='check_and_get_response'>TokenRequiredFieldHandler.check_and_get_response</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t84">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t84"><data value='init__'>ClientNameHandler.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t87">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t87"><data value='check_and_get_response'>ClientNameHandler.check_and_get_response</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t215">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t215"><data value='init__'>LDMFCountryHandler.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t218">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t218"><data value='check_and_get_response'>LDMFCountryHandler.check_and_get_response</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t299">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t299"><data value='check_and_get_response'>DateIntervalsHandler.check_and_get_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t315">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t315"><data value='check_and_get_response'>ObjectiveHandler.check_and_get_response</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t349">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t349"><data value='check_and_get_response'>OutcomesHandler.check_and_get_response</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8___init___py.html">app/services/extracted_data/parsers/__init__.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t17">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t17"><data value='call__'>KXDashDataParser.__call__</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t34">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t34"><data value='get_date_for_extracted_data'>KXDashDataParser._get_date_for_extracted_data</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t49">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t49"><data value='get_json_for_extracted_data'>KXDashDataParser._get_json_for_extracted_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t16">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t16"><data value='call__'>PromptAndDocumentDataParser.__call__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t53">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t53"><data value='get_date_for_extracted_data'>PromptAndDocumentDataParser._get_date_for_extracted_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t77">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t77"><data value='init__'>ExtractedDataService.__init__</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t97">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t97"><data value='delete_many'>ExtractedDataService.delete_many</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t114">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t114"><data value='process_activity_data'>ExtractedDataService._process_activity_data</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t195">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t195"><data value='update'>ExtractedDataService.update</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>3</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t227">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t227"><data value='aggregate_data'>ExtractedDataService.aggregate_data</data></a></td>
                <td>26</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="8 26">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t291">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t291"><data value='get_missing_required_data_prompts'>ExtractedDataService.get_missing_required_data_prompts</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t396">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t396"><data value='get_all_missing_fields'>ExtractedDataService._get_all_missing_fields</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t411">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t411"><data value='list'>ExtractedDataService.list</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t430">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t430"><data value='delete'>ExtractedDataService.delete</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t449">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t449"><data value='update_confirmed_data'>ExtractedDataService.update_confirmed_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t489">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t489"><data value='call_handler_check_and_get_response'>ExtractedDataService._call_handler_check_and_get_response</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t71">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t71"><data value='get_user_prompt'>IntentClassifierService._get_user_prompt</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t80">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t80"><data value='get_system_prompt'>IntentClassifierService._get_system_prompt</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t92">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t92"><data value='get_formatted_intents_info'>IntentClassifierService._get_formatted_intents_info</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t105">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t105"><data value='get_intents_info'>IntentClassifierService.get_intents_info</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t125">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t125"><data value='create_system_message'>IntentClassifierService._create_system_message</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t130">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t130"><data value='create_user_message'>IntentClassifierService._create_user_message</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t134">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t134"><data value='classify_intent'>IntentClassifierService.classify_intent</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657___init___py.html">app/services/kx_dash/__init__.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t13">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t13"><data value='call__'>KXDashMessageFormatter.__call__</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t78">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t78"><data value='get_section'>KXDashMessageFormatter._get_section</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t98">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t98"><data value='get_detail'>KXDashMessageFormatter._get_detail</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t43">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t43"><data value='init__'>KXDashService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t58">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t58"><data value='list'>KXDashService.list</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>3</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t96">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t96"><data value='get'>KXDashService.get</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>3</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t124">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t124"><data value='on_select'>KXDashService.on_select</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>3</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t175">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t175"><data value='extract_activity_details'>KXDashService._extract_activity_details</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t78">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t78"><data value='get_intent'>ConversationMessageProcessor._get_intent</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t90">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t90"><data value='run'>ConversationMessageProcessor.run</data></a></td>
                <td>24</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="8 24">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t131">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t131"><data value='process_undefined'>ConversationMessageProcessor._process_undefined</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t134">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t134"><data value='generate_qual'>ConversationMessageProcessor._generate_qual</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t188">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t188"><data value='extract_data'>ConversationMessageProcessor._extract_data</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t310">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t310"><data value='dash_show_more'>ConversationMessageProcessor._dash_show_more</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t318">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t318"><data value='dash_discard'>ConversationMessageProcessor._dash_discard</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t322">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t322"><data value='example_help'>ConversationMessageProcessor._example_help</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t325">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t325"><data value='uncertainty'>ConversationMessageProcessor._uncertainty</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t328">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t328"><data value='detect_client_name_from_message'>ConversationMessageProcessor._detect_client_name_from_message</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t367">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t367"><data value='analyze_dates_message'>ConversationMessageProcessor._analyze_dates_message</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t394">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t394"><data value='handle_dates_input'>ConversationMessageProcessor._handle_dates_input</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t428">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t428"><data value='handle_client_name_input'>ConversationMessageProcessor._handle_client_name_input</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t512">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t512"><data value='handle_client_creation_response'>ConversationMessageProcessor._handle_client_creation_response</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t618">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t618"><data value='handle_objective_and_scope_input'>ConversationMessageProcessor._handle_objective_and_scope_input</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t642">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t642"><data value='handle_outcomes_input'>ConversationMessageProcessor._handle_outcomes_input</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t666">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t666"><data value='handle_country_input'>ConversationMessageProcessor._handle_country_input</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t713">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t713"><data value='handle_data_complete_response'>ConversationMessageProcessor._handle_data_complete_response</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="54 54">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t15">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t15"><data value='init__'>RoleDataService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t19">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t19"><data value='set_cached_roles'>RoleDataService._set_cached_roles</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t22">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t22"><data value='list'>RoleDataService.list</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t15">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t15"><data value='init__'>ServiceDataService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t19">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t19"><data value='set_cached_services'>ServiceDataService._set_cached_services</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t22">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t22"><data value='list'>ServiceDataService.list</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t29">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t29"><data value='run'>SuggestedPromptsGenerator.run</data></a></td>
                <td>37</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="25 37">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t146">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t146"><data value='user_provide_input_or_client'>SuggestedPromptsGenerator._user_provide_input_or_client</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t150">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t150"><data value='user_provide_input_or_ldmf_country'>SuggestedPromptsGenerator._user_provide_input_or_ldmf_country</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t154">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t154"><data value='user_selects_brief_description'>SuggestedPromptsGenerator._user_selects_brief_description</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t158">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t158"><data value='user_has_dash_tasks'>SuggestedPromptsGenerator._user_has_dash_tasks</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t164">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t164"><data value='user_navigates_from_dash_task'>SuggestedPromptsGenerator._user_navigates_from_dash_task</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t177">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t177"><data value='user_provided_all_required_fields'>SuggestedPromptsGenerator._user_provided_all_required_fields</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t190">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t190"><data value='ai_detected_multiple_client_name_variations'>SuggestedPromptsGenerator._ai_detected_multiple_client_name_variations</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t194">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t194"><data value='ai_detected_multiple_ldmf_country_variations'>SuggestedPromptsGenerator._ai_detected_multiple_ldmf_country_variations</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t198">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t198"><data value='ai_retrieves_single_client_name'>SuggestedPromptsGenerator._ai_retrieves_single_client_name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t202">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t202"><data value='ai_retrieves_single_ldmf_country'>SuggestedPromptsGenerator._ai_retrieves_single_ldmf_country</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t206">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t206"><data value='ai_couldnt_find_provided_client_name'>SuggestedPromptsGenerator._ai_couldnt_find_provided_client_name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t210">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t210"><data value='ai_couldnt_find_ldmf_country_variations'>SuggestedPromptsGenerator._ai_couldnt_find_ldmf_country_variations</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00___init___py.html">app/tests/__init__.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t21">app/tests/conftest.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t21"><data value='async_client'>async_client</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t29">app/tests/conftest.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t29"><data value='url_resolver'>url_resolver</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t34">app/tests/conftest.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html#t34"><data value='pytest_collection_modifyitems'>pytest_collection_modifyitems</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html">app/tests/conftest.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="16 17">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5___init___py.html">app/tests/fixtures/__init__.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t20">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t20"><data value='get_random_ipv4'>_get_random_ipv4</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t24">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t24"><data value='get_random_onprem_sid'>_get_random_onprem_sid</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t33">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t33"><data value='get_random_secret'>_get_random_secret</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t38">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t38"><data value='decoded_jwt_token'>decoded_jwt_token</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t71">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t71"><data value='auth_header'>auth_header</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t76">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html#t76"><data value='auth_mock'>auth_mock</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t15">app/tests/fixtures/conversation.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t15"><data value='conversation_data'>conversation_data</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t23">app/tests/fixtures/conversation.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t23"><data value='test_conversation_id'>test_conversation_id</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t38">app/tests/fixtures/conversation.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html#t38"><data value='conversation_repository_dep'>conversation_repository_dep</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html">app/tests/fixtures/conversation.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t16">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t16"><data value='create_test_database'>_create_test_database</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t34">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t34"><data value='run_migrations'>_run_migrations</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t40">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t40"><data value='drop_test_database'>_drop_test_database</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t61">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t61"><data value='setup_test_db'>setup_test_db</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t71">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t71"><data value='db_engine'>db_engine</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t83">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html#t83"><data value='db_session'>db_session</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t38">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t38"><data value='industry_data_service'>industry_data_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t44">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t44"><data value='role_data_service'>role_data_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t50">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t50"><data value='service_data_service'>service_data_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t56">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t56"><data value='extracted_data_repository'>extracted_data_repository</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t61">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t61"><data value='ldmf_countries_repository'>ldmf_countries_repository</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t69">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t69"><data value='extracted_data_service'>extracted_data_service</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t90">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t90"><data value='kx_dash_extracted_data'>kx_dash_extracted_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t110">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t110"><data value='documents_extracted_data'>documents_extracted_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t130">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t130"><data value='prompt_extracted_data'>prompt_extracted_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t150">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t150"><data value='init__'>AutoCommitRepository.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t154">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t154"><data value='getattr__'>AutoCommitRepository.__getattr__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t159">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t159"><data value='wrapper'>AutoCommitRepository.__getattr__.wrapper</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t168">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t168"><data value='extracted_data_repository_real_with_autocommit'>extracted_data_repository_real_with_autocommit</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t178">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t178"><data value='conversation_repository_real_with_autocommit'>conversation_repository_real_with_autocommit</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t188">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t188"><data value='extracted_data_repository_real'>extracted_data_repository_real</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t194">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t194"><data value='extracted_data_service_real'>extracted_data_service_real</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t20">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t20"><data value='mock_kx_dash_service'>mock_kx_dash_service</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t29">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t29"><data value='kx_dash_repository'>kx_dash_repository</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t38">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t38"><data value='extracted_data_service_mock'>extracted_data_service_mock</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t48">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t48"><data value='kx_dash_service_with_repo'>kx_dash_service_with_repo</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t60">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t60"><data value='mock_activity'>mock_activity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t85">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html#t85"><data value='mock_activities_response'>mock_activities_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html#t11">app/tests/fixtures/openai.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html#t11"><data value='openai_mock'>openai_mock</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html#t15">app/tests/fixtures/openai.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html#t15"><data value='mock_generate'>openai_mock.mock_generate</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html">app/tests/fixtures/openai.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t42">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t42"><data value='test_create_conversation_success'>test_create_conversation_success</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t103">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t103"><data value='test_get_conversation_success'>test_get_conversation_success</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t133">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t133"><data value='test_get_conversation_last_message_not_found'>test_get_conversation_last_message_not_found</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t158">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t158"><data value='test_get_conversation_not_found'>test_get_conversation_not_found</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t175">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t175"><data value='test_get_conversation_invalid_uuid'>test_get_conversation_invalid_uuid</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t189">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t189"><data value='test_get_conversation_invalid_user_uuid'>test_get_conversation_invalid_user_uuid</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t222">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t222"><data value='test_create_conversation_validation_errors'>test_create_conversation_validation_errors</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t237">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t237"><data value='test_delete_conversation_basic_functionality'>test_delete_conversation_basic_functionality</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t260">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t260"><data value='test_delete_conversation_not_found'>test_delete_conversation_not_found</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t277">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t277"><data value='test_delete_conversation_invalid_uuid'>test_delete_conversation_invalid_uuid</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t291">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t291"><data value='test_get_conversation_messages_success'>test_get_conversation_messages_success</data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t336">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t336"><data value='test_get_conversation_last_message'>test_get_conversation_last_message</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t372">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t372"><data value='test_get_conversation_messages_not_found'>test_get_conversation_messages_not_found</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t402">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t402"><data value='test_get_conversation_messages_malformed_uuid'>test_get_conversation_messages_malformed_uuid</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t418">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t418"><data value='test_get_conversation_messages_wrong_user_id'>test_get_conversation_messages_wrong_user_id</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t444">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t444"><data value='test_delete_conversation_deletes_all_messages'>test_delete_conversation_deletes_all_messages</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t484">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t484"><data value='test_delete_all_conversation_messages_wrong_user_id'>test_delete_all_conversation_messages_wrong_user_id</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t523">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t523"><data value='test_create_conversation_wrong_authentication'>test_create_conversation_wrong_authentication</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t550">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t550"><data value='test_create_conversation_wrong_authentication_token_scopes'>test_create_conversation_wrong_authentication_token_scopes</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t569">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t569"><data value='update_conversation_for_debug'>_update_conversation_for_debug</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t612">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html#t612"><data value='test_get_conversationby_id_for_debug'>test_get_conversationby_id_for_debug</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4050</td>
                <td>1318</td>
                <td>205</td>
                <td class="right" data-ratio="2732 4050">67%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
