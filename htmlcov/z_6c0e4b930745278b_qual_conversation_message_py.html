<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for app/models/qual_conversation_message.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>app/models/qual_conversation_message.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">23 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">23<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_6c0e4b930745278b_qual_conversation_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_6c0e4b930745278b_qual_document_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">import</span> <span class="nam">sqlalchemy</span> <span class="key">as</span> <span class="nam">sa</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">orm</span> <span class="key">import</span> <span class="nam">relationship</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">schema</span> <span class="key">import</span> <span class="nam">Index</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">constants</span><span class="op">.</span><span class="nam">message</span> <span class="key">import</span> <span class="nam">ConversationMessageIntention</span><span class="op">,</span> <span class="nam">MessageRole</span><span class="op">,</span> <span class="nam">MessageType</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">core</span><span class="op">.</span><span class="nam">db</span> <span class="key">import</span> <span class="nam">Base</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="nam">__all__</span> <span class="op">=</span> <span class="op">[</span><span class="str">'QualConversationMessage'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">class</span> <span class="nam">QualConversationMessage</span><span class="op">(</span><span class="nam">Base</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="nam">__tablename__</span> <span class="op">=</span> <span class="str">'QualConversationMessage'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="nam">Id</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">Integer</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">autoincrement</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="nam">PublicId</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">Uuid</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">server_default</span><span class="op">=</span><span class="nam">sa</span><span class="op">.</span><span class="nam">text</span><span class="op">(</span><span class="str">'NEWID()'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">QualConversationId</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">Integer</span><span class="op">,</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="str">'QualConversation.Id'</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="nam">Role</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">Enum</span><span class="op">(</span><span class="nam">MessageRole</span><span class="op">,</span> <span class="nam">values_callable</span><span class="op">=</span><span class="key">lambda</span> <span class="nam">x</span><span class="op">:</span> <span class="op">[</span><span class="nam">e</span><span class="op">.</span><span class="nam">value</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">x</span><span class="op">]</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">    <span class="nam">Type</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">Enum</span><span class="op">(</span><span class="nam">MessageType</span><span class="op">,</span> <span class="nam">values_callable</span><span class="op">=</span><span class="key">lambda</span> <span class="nam">x</span><span class="op">:</span> <span class="op">[</span><span class="nam">e</span><span class="op">.</span><span class="nam">value</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">x</span><span class="op">]</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="nam">Content</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">UnicodeText</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">Options</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">UnicodeText</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>  <span class="com"># NOTE: For system messages only</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">SelectedOption</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">UnicodeText</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>  <span class="com"># NOTE: For user messages only</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">CreatedAt</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">DateTime</span><span class="op">,</span> <span class="nam">server_default</span><span class="op">=</span><span class="nam">sa</span><span class="op">.</span><span class="nam">func</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">Intention</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">        <span class="nam">sa</span><span class="op">.</span><span class="nam">Enum</span><span class="op">(</span><span class="nam">ConversationMessageIntention</span><span class="op">,</span> <span class="nam">values_callable</span><span class="op">=</span><span class="key">lambda</span> <span class="nam">x</span><span class="op">:</span> <span class="op">[</span><span class="nam">e</span><span class="op">.</span><span class="nam">value</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">x</span><span class="op">]</span><span class="op">)</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">SuggestedPrompts</span> <span class="op">=</span> <span class="nam">sa</span><span class="op">.</span><span class="nam">Column</span><span class="op">(</span><span class="nam">sa</span><span class="op">.</span><span class="nam">UnicodeText</span><span class="op">,</span> <span class="nam">nullable</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'[]'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">Conversation</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="str">'QualConversation'</span><span class="op">,</span> <span class="nam">back_populates</span><span class="op">=</span><span class="str">'Messages'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">Documents</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="str">'QualDocument'</span><span class="op">,</span> <span class="nam">back_populates</span><span class="op">=</span><span class="str">'Message'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">ProcessingMessages</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">(</span><span class="str">'QualProcessingMessage'</span><span class="op">,</span> <span class="nam">back_populates</span><span class="op">=</span><span class="str">'ConversationMessage'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="nam">Index</span><span class="op">(</span><span class="str">'IX__QualConversationMessage__CreatedAt_Id'</span><span class="op">,</span> <span class="nam">QualConversationMessage</span><span class="op">.</span><span class="nam">CreatedAt</span><span class="op">,</span> <span class="nam">QualConversationMessage</span><span class="op">.</span><span class="nam">Id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_6c0e4b930745278b_qual_conversation_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_6c0e4b930745278b_qual_document_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
</footer>
</body>
</html>
