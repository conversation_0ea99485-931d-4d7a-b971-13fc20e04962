{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "067ae03c336f3bb431283b9599327a1b", "files": {"z_cfb6adc3f81c8e3c___init___py": {"hash": "945b5ab731d31ab1e5d2dce9fc901d5e", "index": {"url": "z_cfb6adc3f81c8e3c___init___py.html", "file": "app/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_auth_py": {"hash": "c44bbe9d9e0edef10ee13c8a3bdfb02c", "index": {"url": "z_cfb6adc3f81c8e3c_auth_py.html", "file": "app/api/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_conversation_py": {"hash": "e027c38891721110825dfbbf00655956", "index": {"url": "z_cfb6adc3f81c8e3c_conversation_py.html", "file": "app/api/conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 20, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_conversation_debug_py": {"hash": "23e7fdb76f7a6e2882190cbd85e59142", "index": {"url": "z_cfb6adc3f81c8e3c_conversation_debug_py.html", "file": "app/api/conversation_debug.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 19, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_kx_dash_py": {"hash": "136a84a57067673db7f38fee93af3840", "index": {"url": "z_cfb6adc3f81c8e3c_kx_dash_py.html", "file": "app/api/kx_dash.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_message_py": {"hash": "c3795c8fe9a139ce641337f0d9dd9621", "index": {"url": "z_cfb6adc3f81c8e3c_message_py.html", "file": "app/api/message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 8, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_root_py": {"hash": "bb04df32a5d25a18ba06f5754ebae58d", "index": {"url": "z_cfb6adc3f81c8e3c_root_py.html", "file": "app/api/root.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce___init___py": {"hash": "dc4b6f941e8403e2ccb98cb07a637fc4", "index": {"url": "z_92724abef7b332ce___init___py.html", "file": "app/config/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce_app_settings_py": {"hash": "0256353f36033ad84cd4455ff45b35c7", "index": {"url": "z_92724abef7b332ce_app_settings_py.html", "file": "app/config/app_settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce_logging_py": {"hash": "8dc1fd1c462ee59a01c89053e68514f5", "index": {"url": "z_92724abef7b332ce_logging_py.html", "file": "app/config/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce_openapi_py": {"hash": "009eca69faf2666241011b7b32da56ff", "index": {"url": "z_92724abef7b332ce_openapi_py.html", "file": "app/config/openapi.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b964701b87808fad_auth_py": {"hash": "d73e5d7059c2e8065a3081913de9fd71", "index": {"url": "z_b964701b87808fad_auth_py.html", "file": "app/constants/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b964701b87808fad_environment_py": {"hash": "b0cdd8fdb8051a5554ea69814f48b974", "index": {"url": "z_b964701b87808fad_environment_py.html", "file": "app/constants/environment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b964701b87808fad_extracted_data_py": {"hash": "d83595b58eb8fb0cd54430c9d3ed0149", "index": {"url": "z_b964701b87808fad_extracted_data_py.html", "file": "app/constants/extracted_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b964701b87808fad_message_py": {"hash": "3f371a9bfe50c9cfea5b9e3f6b60ad8a", "index": {"url": "z_b964701b87808fad_message_py.html", "file": "app/constants/message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b964701b87808fad_operation_ids_py": {"hash": "26e5eb8d144826dc9c79be05219a73bd", "index": {"url": "z_b964701b87808fad_operation_ids_py.html", "file": "app/constants/operation_ids.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_8f7e1016f2d37417___init___py.html", "file": "app/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_db_py": {"hash": "036a6ca11aba03783b2e050eb2db3b06", "index": {"url": "z_8f7e1016f2d37417_db_py.html", "file": "app/core/db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_enum_py": {"hash": "c15c60699008dbfa497fbb5a577c7554", "index": {"url": "z_8f7e1016f2d37417_enum_py.html", "file": "app/core/enum.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_json_py": {"hash": "21b3b6c240d763d763a6cbd3152bafb8", "index": {"url": "z_8f7e1016f2d37417_json_py.html", "file": "app/core/json.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_schemas_py": {"hash": "4d351514b1c619f6b17861f480f58970", "index": {"url": "z_8f7e1016f2d37417_schemas_py.html", "file": "app/core/schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_urls_py": {"hash": "faf2d8f0a713df7dc1b48eab4a431cbb", "index": {"url": "z_8f7e1016f2d37417_urls_py.html", "file": "app/core/urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78bab267e2af3bd7___init___py": {"hash": "01305102d0aa59155eede2f93478df9b", "index": {"url": "z_78bab267e2af3bd7___init___py.html", "file": "app/core/validators/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78bab267e2af3bd7_document_files_py": {"hash": "c53e7b45730b1e0656b1af0a3df2dad0", "index": {"url": "z_78bab267e2af3bd7_document_files_py.html", "file": "app/core/validators/document_files.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_78bab267e2af3bd7_suggested_prompts_py": {"hash": "363e59aa4c11145a06fd1f04c154135d", "index": {"url": "z_78bab267e2af3bd7_suggested_prompts_py.html", "file": "app/core/validators/suggested_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14___init___py": {"hash": "cd7845f6461ef3ac407a4f68d7a9828e", "index": {"url": "z_b695b9fb39a9ef14___init___py.html", "file": "app/dependencies/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14_db_py": {"hash": "355f1996fb1b5201f80b70c75448c8c1", "index": {"url": "z_b695b9fb39a9ef14_db_py.html", "file": "app/dependencies/db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14_http_client_py": {"hash": "92b12028db52a7b686de78d3abd6a51f", "index": {"url": "z_b695b9fb39a9ef14_http_client_py.html", "file": "app/dependencies/http_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14_permissions_py": {"hash": "3ca8540000c48c8951c96e5026dd5a56", "index": {"url": "z_b695b9fb39a9ef14_permissions_py.html", "file": "app/dependencies/permissions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14_repositories_py": {"hash": "a958b7d7060cf6baeb000df080c44d21", "index": {"url": "z_b695b9fb39a9ef14_repositories_py.html", "file": "app/dependencies/repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695b9fb39a9ef14_services_py": {"hash": "30db749ecc8b3bfea392e1bf76b303fb", "index": {"url": "z_b695b9fb39a9ef14_services_py.html", "file": "app/dependencies/services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d27bd2b5f4cd408___init___py": {"hash": "b317950b40a065c21b5820b455da2999", "index": {"url": "z_9d27bd2b5f4cd408___init___py.html", "file": "app/exceptions/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d27bd2b5f4cd408_base_py": {"hash": "a58b90fd471cbeb50efbb0cea78fcb47", "index": {"url": "z_9d27bd2b5f4cd408_base_py.html", "file": "app/exceptions/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d27bd2b5f4cd408_document_py": {"hash": "3e85fdcf5c03e47736f95e22d547694a", "index": {"url": "z_9d27bd2b5f4cd408_document_py.html", "file": "app/exceptions/document.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d27bd2b5f4cd408_entity_py": {"hash": "fe2def4e1c2c8a8307e91da28f4e591e", "index": {"url": "z_9d27bd2b5f4cd408_entity_py.html", "file": "app/exceptions/entity.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9d27bd2b5f4cd408_kx_dash_py": {"hash": "b5a1302203954dbcc4642b96b961b02a", "index": {"url": "z_9d27bd2b5f4cd408_kx_dash_py.html", "file": "app/exceptions/kx_dash.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "69cc8c1d81a7a2f1fef1042cf5946348", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56779b6d7779cea4___init___py": {"hash": "000e4e7b21b85c0c10713dcd7c294569", "index": {"url": "z_56779b6d7779cea4___init___py.html", "file": "app/middleware/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56779b6d7779cea4_auth_py": {"hash": "9414b4f1aad9d7cded900cf59d4d61c9", "index": {"url": "z_56779b6d7779cea4_auth_py.html", "file": "app/middleware/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56779b6d7779cea4_exc_to_resp_py": {"hash": "d41216a3bca8551376c9befd0fd54dab", "index": {"url": "z_56779b6d7779cea4_exc_to_resp_py.html", "file": "app/middleware/exc_to_resp.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_05b174b1657229b7_env_py": {"hash": "b8b2626635540c6b09b2c9973d41a61c", "index": {"url": "z_05b174b1657229b7_env_py.html", "file": "app/migrations/env.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py": {"hash": "e1e40c45768f3bcdf0d88ecdc4f7ce3f", "index": {"url": "z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html", "file": "app/migrations/versions/0001_create_a_conversation_and_message_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0002_add_document_model_py": {"hash": "1d17a976bc2b147c8c2eb43153c480ba", "index": {"url": "z_08e801d2950cbe24_0002_add_document_model_py.html", "file": "app/migrations/versions/0002_add_document_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0003_message_change_py": {"hash": "f98052187176f5b1598d568cefbe808e", "index": {"url": "z_08e801d2950cbe24_0003_message_change_py.html", "file": "app/migrations/versions/0003_message_change.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0004_add_model_for_extracted_data_py": {"hash": "8bd779a7148a2555d9e2213a59971c16", "index": {"url": "z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html", "file": "app/migrations/versions/0004_add_model_for_extracted_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0005_new_columns_in_message_py": {"hash": "3be3eadec888dbcf0f148143fb7dc64a", "index": {"url": "z_08e801d2950cbe24_0005_new_columns_in_message_py.html", "file": "app/migrations/versions/0005_new_columns_in_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py": {"hash": "a4faf4ac6fa30034bfa520fcb4726be0", "index": {"url": "z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html", "file": "app/migrations/versions/0006_qualid_in_conversation_made_nullable.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0007_add_processing_table_py": {"hash": "ee720d3f7e265d7b7ed713a5b755c804", "index": {"url": "z_08e801d2950cbe24_0007_add_processing_table_py.html", "file": "app/migrations/versions/0007_add_processing_table.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py": {"hash": "6de388732a68915b19f9503398fdcbd9", "index": {"url": "z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html", "file": "app/migrations/versions/0008_add_intention_to_qualconversationmessage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py": {"hash": "99872c5249e08e25ecaa6d037ae9d2a6", "index": {"url": "z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html", "file": "app/migrations/versions/0009_index_on_createdat_and_id_on_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py": {"hash": "a56d33d6fd58e05fc8744073ae12dba8", "index": {"url": "z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html", "file": "app/migrations/versions/0010_add_columns_to_qualextracteddata.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py": {"hash": "1dda78d58f13d359a43c70f21a969e78", "index": {"url": "z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html", "file": "app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0012_add_activity_id_field_py": {"hash": "bc7f9c349995f5b697a2a898de1a76dd", "index": {"url": "z_08e801d2950cbe24_0012_add_activity_id_field_py.html", "file": "app/migrations/versions/0012_add_activity_id_field.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0013_add_industries_services_and_roles__py": {"hash": "069f577a87445b085d99c17151b2073f", "index": {"url": "z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html", "file": "app/migrations/versions/0013_add_industries_services_and_roles_.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08e801d2950cbe24_0014_add_suggested_prompts_py": {"hash": "f70ffde300c09ba4067670a3d57ced45", "index": {"url": "z_08e801d2950cbe24_0014_add_suggested_prompts_py.html", "file": "app/migrations/versions/0014_add_suggested_prompts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b___init___py": {"hash": "4a4469c45cec8715925a4d43035c9ae0", "index": {"url": "z_6c0e4b930745278b___init___py.html", "file": "app/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_qual_conversation_py": {"hash": "510d6107485756db0f33bd3334cbe5ac", "index": {"url": "z_6c0e4b930745278b_qual_conversation_py.html", "file": "app/models/qual_conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_qual_conversation_message_py": {"hash": "3eb3f73c9845724fcf35b00e0ebfa059", "index": {"url": "z_6c0e4b930745278b_qual_conversation_message_py.html", "file": "app/models/qual_conversation_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_qual_document_py": {"hash": "9949a80cf0b59c6110241f6f929d2d97", "index": {"url": "z_6c0e4b930745278b_qual_document_py.html", "file": "app/models/qual_document.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_qual_extracted_data_py": {"hash": "1f671c08cde85cc2a47f2fd3d875f5ec", "index": {"url": "z_6c0e4b930745278b_qual_extracted_data_py.html", "file": "app/models/qual_extracted_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_qual_processing_message_py": {"hash": "9289b2718d30ae9e62fcc74fd63e90eb", "index": {"url": "z_6c0e4b930745278b_qual_processing_message_py.html", "file": "app/models/qual_processing_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f___init___py": {"hash": "da518cbe0c092a859f82d140390151e7", "index": {"url": "z_4eed7ad336a4b97f___init___py.html", "file": "app/repositories/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_conversation_py": {"hash": "075e161ed7b52bda4536fb70660d5d91", "index": {"url": "z_4eed7ad336a4b97f_conversation_py.html", "file": "app/repositories/conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_conversation_message_py": {"hash": "3806e2fc46a68b11cabb2334f138a908", "index": {"url": "z_4eed7ad336a4b97f_conversation_message_py.html", "file": "app/repositories/conversation_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_document_blob_py": {"hash": "d409521955925a8bd448d78422dfcca3", "index": {"url": "z_4eed7ad336a4b97f_document_blob_py.html", "file": "app/repositories/document_blob.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 6, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_document_db_py": {"hash": "54ebf18fb2a0827688fd8a7833aea478", "index": {"url": "z_4eed7ad336a4b97f_document_db_py.html", "file": "app/repositories/document_db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_document_queue_py": {"hash": "7be7a6ca193c0d33c50b3b3a96ff7274", "index": {"url": "z_4eed7ad336a4b97f_document_queue_py.html", "file": "app/repositories/document_queue.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_extracted_data_py": {"hash": "e435151dbd5bdc7af6d74ac2e13c43a5", "index": {"url": "z_4eed7ad336a4b97f_extracted_data_py.html", "file": "app/repositories/extracted_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_industry_py": {"hash": "765c903522bbd8ba6c61c754344284ab", "index": {"url": "z_4eed7ad336a4b97f_industry_py.html", "file": "app/repositories/industry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_kx_dash_py": {"hash": "157e5a72d989dcfd7bddc01aa195d82c", "index": {"url": "z_4eed7ad336a4b97f_kx_dash_py.html", "file": "app/repositories/kx_dash.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_ldmf_countries_py": {"hash": "38d0bc8e3c4dfb561a24ed2c1c7b2638", "index": {"url": "z_4eed7ad336a4b97f_ldmf_countries_py.html", "file": "app/repositories/ldmf_countries.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_openai_py": {"hash": "c438588d0a284f5c3f3490fd85988920", "index": {"url": "z_4eed7ad336a4b97f_openai_py.html", "file": "app/repositories/openai.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 40, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_processing_message_py": {"hash": "c2cf4ca5ac979bf84423d2b46f1e1e60", "index": {"url": "z_4eed7ad336a4b97f_processing_message_py.html", "file": "app/repositories/processing_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 40, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_quals_clients_py": {"hash": "8ea944e3cabe0ac6096ddbd2b3e70d58", "index": {"url": "z_4eed7ad336a4b97f_quals_clients_py.html", "file": "app/repositories/quals_clients.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_role_py": {"hash": "f856e5060df42b0a7a93a75e2a73d08c", "index": {"url": "z_4eed7ad336a4b97f_role_py.html", "file": "app/repositories/role.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4eed7ad336a4b97f_service_py": {"hash": "d5c30e0046900745bc8f37f43e141ccd", "index": {"url": "z_4eed7ad336a4b97f_service_py.html", "file": "app/repositories/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c___init___py": {"hash": "b1baabff8630264eff63b77380980c5f", "index": {"url": "z_c0f67d75e686303c___init___py.html", "file": "app/schemas/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_auth_py": {"hash": "8e8dad05b8ff42d87e2f481fb75e3411", "index": {"url": "z_c0f67d75e686303c_auth_py.html", "file": "app/schemas/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_confirmed_data_py": {"hash": "47059c12a769390a1809ebd990b5d6b1", "index": {"url": "z_c0f67d75e686303c_confirmed_data_py.html", "file": "app/schemas/confirmed_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_conversation_py": {"hash": "5ebd312e88371191d905b3c5287abe54", "index": {"url": "z_c0f67d75e686303c_conversation_py.html", "file": "app/schemas/conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41e01f802a67a65f___init___py": {"hash": "9175b09c8d34247abef142768207d57a", "index": {"url": "z_41e01f802a67a65f___init___py.html", "file": "app/schemas/conversation_message/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41e01f802a67a65f_message_py": {"hash": "5b9e212529ea6ada7241199d8fc086f2", "index": {"url": "z_41e01f802a67a65f_message_py.html", "file": "app/schemas/conversation_message/message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41e01f802a67a65f_option_py": {"hash": "d4024e49436dd1f0fd46b3ea929b7695", "index": {"url": "z_41e01f802a67a65f_option_py.html", "file": "app/schemas/conversation_message/option.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_dash_task_py": {"hash": "1196ae9f32972a5c7f53d9a1f1c074fd", "index": {"url": "z_c0f67d75e686303c_dash_task_py.html", "file": "app/schemas/dash_task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_dates_py": {"hash": "68048a62dd7e6f9d50d33b737546e946", "index": {"url": "z_c0f67d75e686303c_dates_py.html", "file": "app/schemas/dates.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_document_py": {"hash": "991ff75d8846875d29c2dc4833547c31", "index": {"url": "z_c0f67d75e686303c_document_py.html", "file": "app/schemas/document.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_extracted_data_py": {"hash": "742591da6025151b42152f8946ee0dde", "index": {"url": "z_c0f67d75e686303c_extracted_data_py.html", "file": "app/schemas/extracted_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_ldmf_countries_py": {"hash": "d56124f4cd2a6f092557ceee79742495", "index": {"url": "z_c0f67d75e686303c_ldmf_countries_py.html", "file": "app/schemas/ldmf_countries.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_processing_py": {"hash": "030b408c0f0b2604ac3e1c23e0c3f10a", "index": {"url": "z_c0f67d75e686303c_processing_py.html", "file": "app/schemas/processing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_quals_clients_py": {"hash": "7faddc77abcf017d671338b570a85adf", "index": {"url": "z_c0f67d75e686303c_quals_clients_py.html", "file": "app/schemas/quals_clients.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_root_py": {"hash": "478cc8121c49bbd1d2f534609980d320", "index": {"url": "z_c0f67d75e686303c_root_py.html", "file": "app/schemas/root.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "623b6d8f1d5db5b6c377d73003b84967", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_auth_py": {"hash": "1dd9f7ebc9ca85f28895aa6ecc8c3b08", "index": {"url": "z_c318f3fa19a49f69_auth_py.html", "file": "app/services/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_client_industry_py": {"hash": "093e0f664c31fc561ae55eab39ddbd32", "index": {"url": "z_c318f3fa19a49f69_client_industry_py.html", "file": "app/services/client_industry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_conversation_py": {"hash": "3d082e72bdf913956b628a72ff2fc142", "index": {"url": "z_c318f3fa19a49f69_conversation_py.html", "file": "app/services/conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 25, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_conversation_message_py": {"hash": "0591c2aac513ead83ee1027056b17d90", "index": {"url": "z_c318f3fa19a49f69_conversation_message_py.html", "file": "app/services/conversation_message.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 143, "n_excluded": 21, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_date_validator_py": {"hash": "b5e359d0a17051c26736822d55397659", "index": {"url": "z_c318f3fa19a49f69_date_validator_py.html", "file": "app/services/date_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_document_py": {"hash": "7aa144404f5bc3235fe3f3e49397508c", "index": {"url": "z_c318f3fa19a49f69_document_py.html", "file": "app/services/document.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 9, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_76e91c945518a187___init___py": {"hash": "cb2c5f543a0448573a4e84bc05501d41", "index": {"url": "z_76e91c945518a187___init___py.html", "file": "app/services/extracted_data/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_76e91c945518a187_handlers_py": {"hash": "ec925af815f571e4e3e693c57647086e", "index": {"url": "z_76e91c945518a187_handlers_py.html", "file": "app/services/extracted_data/handlers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 2, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dd1131693b77ced8___init___py": {"hash": "3ace28b493dced3e8edbba58a44cbaba", "index": {"url": "z_dd1131693b77ced8___init___py.html", "file": "app/services/extracted_data/parsers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dd1131693b77ced8_kx_dash_py": {"hash": "00ddb259d8b5e18b508c1b13762c556f", "index": {"url": "z_dd1131693b77ced8_kx_dash_py.html", "file": "app/services/extracted_data/parsers/kx_dash.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_dd1131693b77ced8_prompt_and_documents_py": {"hash": "a15ecd1e341bf84dbca093f145cc77b9", "index": {"url": "z_dd1131693b77ced8_prompt_and_documents_py.html", "file": "app/services/extracted_data/parsers/prompt_and_documents.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_76e91c945518a187_service_py": {"hash": "fddd677b189cb6ba72a9b89b0b10d03c", "index": {"url": "z_76e91c945518a187_service_py.html", "file": "app/services/extracted_data/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 6, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_intent_classifier_py": {"hash": "79990ac122d7d9f9c59e579714a231cb", "index": {"url": "z_c318f3fa19a49f69_intent_classifier_py.html", "file": "app/services/intent_classifier.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7635006ae16a657___init___py": {"hash": "cb2c5f543a0448573a4e84bc05501d41", "index": {"url": "z_e7635006ae16a657___init___py.html", "file": "app/services/kx_dash/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7635006ae16a657_formatter_py": {"hash": "0c8033e6c2ce14ad498f41de49e3ebe8", "index": {"url": "z_e7635006ae16a657_formatter_py.html", "file": "app/services/kx_dash/formatter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e7635006ae16a657_service_py": {"hash": "9063794d2d6dfb8545ac0afc8d9df1a4", "index": {"url": "z_e7635006ae16a657_service_py.html", "file": "app/services/kx_dash/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 9, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_message_processor_py": {"hash": "22ee76a718eb396c83d473565d04785a", "index": {"url": "z_c318f3fa19a49f69_message_processor_py.html", "file": "app/services/message_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 0, "n_missing": 190, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_project_role_py": {"hash": "c972b9087091fc6ea92321825934764e", "index": {"url": "z_c318f3fa19a49f69_project_role_py.html", "file": "app/services/project_role.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_project_service_py": {"hash": "b620afbdf61634fc4415ad492d6776c8", "index": {"url": "z_c318f3fa19a49f69_project_service_py.html", "file": "app/services/project_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_suggestions_py": {"hash": "40758cdf2ba03483156814a3767fc0c6", "index": {"url": "z_c318f3fa19a49f69_suggestions_py.html", "file": "app/services/suggestions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5129300603c71d00___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_5129300603c71d00___init___py.html", "file": "app/tests/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5129300603c71d00_conftest_py": {"hash": "704c2fd6ca05c8cc7c4ae3a7a4d96d4f", "index": {"url": "z_5129300603c71d00_conftest_py.html", "file": "app/tests/conftest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5___init___py": {"hash": "75a323fded513c8f6e9609a2d746c2d8", "index": {"url": "z_45f90b2413c431a5___init___py.html", "file": "app/tests/fixtures/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_auth_py": {"hash": "a639f159643c77d51da6261809fb365b", "index": {"url": "z_45f90b2413c431a5_auth_py.html", "file": "app/tests/fixtures/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_conversation_py": {"hash": "d6b7f14e631cdb7eaef9b4c149fd7b76", "index": {"url": "z_45f90b2413c431a5_conversation_py.html", "file": "app/tests/fixtures/conversation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_db_py": {"hash": "378ccb600c93f85b3e870d167fb7cc2f", "index": {"url": "z_45f90b2413c431a5_db_py.html", "file": "app/tests/fixtures/db.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_extracted_data_service_py": {"hash": "d5f7ccaef2ff3e21bb8b0148c90ec648", "index": {"url": "z_45f90b2413c431a5_extracted_data_service_py.html", "file": "app/tests/fixtures/extracted_data_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 79, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_kx_dash_py": {"hash": "e3552827a41fb4ed05bf008ab3016344", "index": {"url": "z_45f90b2413c431a5_kx_dash_py.html", "file": "app/tests/fixtures/kx_dash.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_45f90b2413c431a5_openai_py": {"hash": "b950863894ff2c98441e6b2565a06982", "index": {"url": "z_45f90b2413c431a5_openai_py.html", "file": "app/tests/fixtures/openai.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5129300603c71d00_test_conversation_endpoints_py": {"hash": "413a46d3351090761bb4b4f5d984cc10", "index": {"url": "z_5129300603c71d00_test_conversation_endpoints_py.html", "file": "app/tests/test_conversation_endpoints.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 266, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}