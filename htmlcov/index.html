<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">67%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html">app/api/auth.py</a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html">app/api/conversation.py</a></td>
                <td>38</td>
                <td>1</td>
                <td>20</td>
                <td class="right" data-ratio="37 38">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html">app/api/conversation_debug.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html">app/api/kx_dash.py</a></td>
                <td>32</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="14 32">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html">app/api/message.py</a></td>
                <td>25</td>
                <td>6</td>
                <td>8</td>
                <td class="right" data-ratio="19 25">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html">app/api/root.py</a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html">app/config/app_settings.py</a></td>
                <td>106</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="104 106">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html">app/config/logging.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html">app/config/openapi.py</a></td>
                <td>17</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="7 17">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html">app/constants/auth.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html">app/constants/environment.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html">app/constants/extracted_data.py</a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html">app/constants/message.py</a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html">app/constants/operation_ids.py</a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_db_py.html">app/core/db.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html">app/core/enum.py</a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html">app/core/json.py</a></td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="12 17">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html">app/core/schemas.py</a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html">app/core/urls.py</a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7___init___py.html">app/core/validators/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html">app/core/validators/document_files.py</a></td>
                <td>24</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="5 24">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html">app/core/validators/suggested_prompts.py</a></td>
                <td>20</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="12 20">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14___init___py.html">app/dependencies/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html">app/dependencies/db.py</a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html">app/dependencies/http_client.py</a></td>
                <td>33</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="24 33">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html">app/dependencies/permissions.py</a></td>
                <td>41</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="29 41">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html">app/dependencies/repositories.py</a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html">app/dependencies/services.py</a></td>
                <td>38</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="37 38">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408___init___py.html">app/exceptions/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html">app/exceptions/base.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html">app/exceptions/document.py</a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html">app/exceptions/entity.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html">app/exceptions/kx_dash.py</a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4___init___py.html">app/middleware/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html">app/middleware/auth.py</a></td>
                <td>124</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="79 124">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html">app/middleware/exc_to_resp.py</a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html">app/migrations/env.py</a></td>
                <td>54</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="29 54">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html">app/migrations/versions/0001_create_a_conversation_and_message_models.py</a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html">app/migrations/versions/0002_add_document_model.py</a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html">app/migrations/versions/0003_message_change.py</a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html">app/migrations/versions/0004_add_model_for_extracted_data.py</a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html">app/migrations/versions/0005_new_columns_in_message.py</a></td>
                <td>16</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html">app/migrations/versions/0006_qualid_in_conversation_made_nullable.py</a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html">app/migrations/versions/0007_add_processing_table.py</a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html">app/migrations/versions/0008_add_intention_to_qualconversationmessage.py</a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html">app/migrations/versions/0009_index_on_createdat_and_id_on_message.py</a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html">app/migrations/versions/0010_add_columns_to_qualextracteddata.py</a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html">app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py</a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html">app/migrations/versions/0012_add_activity_id_field.py</a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html">app/migrations/versions/0013_add_industries_services_and_roles_.py</a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html">app/migrations/versions/0014_add_suggested_prompts.py</a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html">app/models/qual_conversation.py</a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html">app/models/qual_conversation_message.py</a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html">app/models/qual_document.py</a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html">app/models/qual_extracted_data.py</a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html">app/models/qual_processing_message.py</a></td>
                <td>30</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="21 30">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f___init___py.html">app/repositories/__init__.py</a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html">app/repositories/conversation.py</a></td>
                <td>58</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="42 58">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html">app/repositories/conversation_message.py</a></td>
                <td>80</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="34 80">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html">app/repositories/document_blob.py</a></td>
                <td>50</td>
                <td>24</td>
                <td>6</td>
                <td class="right" data-ratio="26 50">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html">app/repositories/document_db.py</a></td>
                <td>52</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="25 52">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html">app/repositories/document_queue.py</a></td>
                <td>43</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="22 43">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html">app/repositories/extracted_data.py</a></td>
                <td>65</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="28 65">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html">app/repositories/industry.py</a></td>
                <td>19</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="12 19">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html">app/repositories/kx_dash.py</a></td>
                <td>49</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="21 49">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html">app/repositories/ldmf_countries.py</a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html">app/repositories/openai.py</a></td>
                <td>14</td>
                <td>0</td>
                <td>40</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html">app/repositories/processing_message.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>40</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html">app/repositories/quals_clients.py</a></td>
                <td>54</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="16 54">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html">app/repositories/role.py</a></td>
                <td>19</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="12 19">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html">app/repositories/service.py</a></td>
                <td>19</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="12 19">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html">app/schemas/__init__.py</a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html">app/schemas/auth.py</a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html">app/schemas/confirmed_data.py</a></td>
                <td>21</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="14 21">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html">app/schemas/conversation.py</a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f___init___py.html">app/schemas/conversation_message/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html">app/schemas/conversation_message/message.py</a></td>
                <td>84</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="79 84">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html">app/schemas/conversation_message/option.py</a></td>
                <td>37</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="29 37">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html">app/schemas/dash_task.py</a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html">app/schemas/dates.py</a></td>
                <td>14</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="8 14">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html">app/schemas/document.py</a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html">app/schemas/extracted_data.py</a></td>
                <td>83</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="57 83">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html">app/schemas/ldmf_countries.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html">app/schemas/processing.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html">app/schemas/quals_clients.py</a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html">app/schemas/root.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html">app/services/auth.py</a></td>
                <td>24</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="16 24">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html">app/services/client_industry.py</a></td>
                <td>22</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="12 22">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html">app/services/conversation.py</a></td>
                <td>65</td>
                <td>16</td>
                <td>25</td>
                <td class="right" data-ratio="49 65">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html">app/services/conversation_message.py</a></td>
                <td>143</td>
                <td>82</td>
                <td>21</td>
                <td class="right" data-ratio="61 143">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html">app/services/date_validator.py</a></td>
                <td>27</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="19 27">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html">app/services/document.py</a></td>
                <td>75</td>
                <td>50</td>
                <td>9</td>
                <td class="right" data-ratio="25 75">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187___init___py.html">app/services/extracted_data/__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html">app/services/extracted_data/handlers.py</a></td>
                <td>95</td>
                <td>65</td>
                <td>2</td>
                <td class="right" data-ratio="30 95">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8___init___py.html">app/services/extracted_data/parsers/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td>40</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="14 40">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td>37</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="11 37">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html">app/services/extracted_data/service.py</a></td>
                <td>189</td>
                <td>137</td>
                <td>6</td>
                <td class="right" data-ratio="52 189">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html">app/services/intent_classifier.py</a></td>
                <td>56</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="52 56">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657___init___py.html">app/services/kx_dash/__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html">app/services/kx_dash/formatter.py</a></td>
                <td>53</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="12 53">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html">app/services/kx_dash/service.py</a></td>
                <td>56</td>
                <td>33</td>
                <td>9</td>
                <td class="right" data-ratio="23 56">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html">app/services/message_processor.py</a></td>
                <td>257</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="67 257">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html">app/services/project_role.py</a></td>
                <td>22</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="12 22">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html">app/services/project_service.py</a></td>
                <td>22</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="12 22">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html">app/services/suggestions.py</a></td>
                <td>80</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="67 80">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00___init___py.html">app/tests/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html">app/tests/conftest.py</a></td>
                <td>23</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="22 23">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5___init___py.html">app/tests/fixtures/__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html">app/tests/fixtures/auth.py</a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html">app/tests/fixtures/conversation.py</a></td>
                <td>19</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="18 19">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html">app/tests/fixtures/db.py</a></td>
                <td>44</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="37 44">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html">app/tests/fixtures/extracted_data_service.py</a></td>
                <td>79</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="59 79">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html">app/tests/fixtures/kx_dash.py</a></td>
                <td>35</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="20 35">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html">app/tests/fixtures/openai.py</a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html">app/tests/test_conversation_endpoints.py</a></td>
                <td>266</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="252 266">95%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>4050</td>
                <td>1318</td>
                <td>205</td>
                <td class="right" data-ratio="2732 4050">67%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_5129300603c71d00_test_conversation_endpoints_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_cfb6adc3f81c8e3c___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
