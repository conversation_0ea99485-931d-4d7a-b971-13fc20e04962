<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">67%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html">app/api/auth.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html">app/api/conversation.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>1</td>
                <td>20</td>
                <td class="right" data-ratio="37 38">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html">app/api/conversation_debug.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_conversation_debug_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html">app/api/kx_dash.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_kx_dash_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="14 32">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html">app/api/message.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>6</td>
                <td>8</td>
                <td class="right" data-ratio="19 25">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html">app/api/root.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_root_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t27">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t27"><data value='ADSettings'>ADSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t34">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t34"><data value='SignalRSettings'>SignalRSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t40">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t40"><data value='AuthSettings'>AuthSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t52">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t52"><data value='DatabaseSettings'>DatabaseSettings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t68">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t68"><data value='BlobStorageSettings'>BlobStorageSettings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t92">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t92"><data value='KXDashAPISettings'>KXDashAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t96">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t96"><data value='QualsClientsAPISettings'>QualsClientsAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t101">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t101"><data value='IndustriesAPISettings'>IndustriesAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t105">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t105"><data value='ServicesAPISettings'>ServicesAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t109">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t109"><data value='RolesAPISettings'>RolesAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t113">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t113"><data value='LDMFCountriesAPISettings'>LDMFCountriesAPISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t117">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t117"><data value='HTTPClientBackoffSettings'>HTTPClientBackoffSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t123">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t123"><data value='HTTPClientSettings'>HTTPClientSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t132">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t132"><data value='QueueSettings'>QueueSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t138">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t138"><data value='OpenAISettings'>OpenAISettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t149">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html#t149"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html">app/config/app_settings.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_app_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="103 104">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html">app/config/logging.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html">app/config/openapi.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_openapi_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="7 17">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html#t7">app/constants/auth.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html#t7"><data value='JwtClaim'>JwtClaim</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html">app/constants/auth.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html#t7">app/constants/environment.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html#t7"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html">app/constants/environment.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_environment_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t14">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t14"><data value='DataSourceType'>DataSourceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t20">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t20"><data value='RequiredField'>RequiredField</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t30">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t30"><data value='ConversationState'>ConversationState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t44">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t44"><data value='FieldStatus'>FieldStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t54">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t54"><data value='MissingDataStatus'>MissingDataStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t62">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html#t62"><data value='ConfirmedDataFields'>ConfirmedDataFields</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html">app/constants/extracted_data.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_extracted_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t54">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t54"><data value='MessageRole'>MessageRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t59">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t59"><data value='MessageType'>MessageType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t124">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t124"><data value='OptionType'>OptionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t131">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t131"><data value='ConversationMessageIntention'>ConversationMessageIntention</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t142">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html#t142"><data value='SuggestedUserPrompt'>SuggestedUserPrompt</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_message_py.html">app/constants/message.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t8">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t8"><data value='AuthOperations'>AuthOperations</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t13">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t13"><data value='ConversationOperations'>ConversationOperations</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t23">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t23"><data value='MessageOperations'>MessageOperations</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t33">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t33"><data value='KXDashOperations'>KXDashOperations</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t39">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t39"><data value='RootOperations'>RootOperations</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t44">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t44"><data value='DocumentOperationIds'>DocumentOperationIds</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t49">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html#t49"><data value='OperationIds'>OperationIds</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html">app/constants/operation_ids.py</a></td>
                <td class="name left"><a href="z_b964701b87808fad_operation_ids_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_db_py.html">app/core/db.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_db_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t8">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t8"><data value='CustomEnum'>CustomEnum</data></a></td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="1 3">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t21">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t21"><data value='StrEnum'>StrEnum</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t26">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html#t26"><data value='IntEnum'>IntEnum</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html">app/core/enum.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_enum_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html#t11">app/core/json.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html#t11"><data value='CustomJSONEncoder'>CustomJSONEncoder</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html">app/core/json.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_json_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html#t16">app/core/schemas.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html#t16"><data value='CustomModel'>CustomModel</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html">app/core/schemas.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t15">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html#t15"><data value='URLResolver'>URLResolver</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html">app/core/urls.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7___init___py.html">app/core/validators/__init__.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html">app/core/validators/document_files.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_document_files_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="5 24">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html">app/core/validators/suggested_prompts.py</a></td>
                <td class="name left"><a href="z_78bab267e2af3bd7_suggested_prompts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="12 20">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14___init___py.html">app/dependencies/__init__.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html">app/dependencies/db.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_db_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t65">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html#t65"><data value='CustomAsyncClient'>CustomAsyncClient</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html">app/dependencies/http_client.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_http_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="19 27">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t14">app/dependencies/permissions.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html#t14"><data value='OwnerOnlyPermission'>OwnerOnlyPermission</data></a></td>
                <td>27</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="15 27">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html">app/dependencies/permissions.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_permissions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html">app/dependencies/repositories.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html">app/dependencies/services.py</a></td>
                <td class="name left"><a href="z_b695b9fb39a9ef14_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="37 38">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408___init___py.html">app/exceptions/__init__.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html#t4">app/exceptions/base.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html#t4"><data value='ApplicationError'>ApplicationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html">app/exceptions/base.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t11">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t11"><data value='MaximumDocumentsNumberExceeded'>MaximumDocumentsNumberExceeded</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t24">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html#t24"><data value='MaximumDocumentsSizeExceeded'>MaximumDocumentsSizeExceeded</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html">app/exceptions/document.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_document_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t13">app/exceptions/entity.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html#t13"><data value='EntityNotFoundError'>EntityNotFoundError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html">app/exceptions/entity.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_entity_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html#t7">app/exceptions/kx_dash.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html#t7"><data value='KXDashDateParsingError'>KXDashDateParsingError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html">app/exceptions/kx_dash.py</a></td>
                <td class="name left"><a href="z_9d27bd2b5f4cd408_kx_dash_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4___init___py.html">app/middleware/__init__.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t26">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t26"><data value='InvalidAuthorization'>InvalidAuthorization</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t35">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html#t35"><data value='AzureADAuthorizerMiddleware'>AzureADAuthorizerMiddleware</data></a></td>
                <td>86</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="41 86">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html">app/middleware/auth.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html#t11">app/middleware/exc_to_resp.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html#t11"><data value='ExceptionToResponseMiddleware'>ExceptionToResponseMiddleware</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html">app/middleware/exc_to_resp.py</a></td>
                <td class="name left"><a href="z_56779b6d7779cea4_exc_to_resp_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html">app/migrations/env.py</a></td>
                <td class="name left"><a href="z_05b174b1657229b7_env_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="29 54">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html">app/migrations/versions/0001_create_a_conversation_and_message_models.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0001_create_a_conversation_and_message_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html">app/migrations/versions/0002_add_document_model.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0002_add_document_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html">app/migrations/versions/0003_message_change.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0003_message_change_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html">app/migrations/versions/0004_add_model_for_extracted_data.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0004_add_model_for_extracted_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html">app/migrations/versions/0005_new_columns_in_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0005_new_columns_in_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html">app/migrations/versions/0006_qualid_in_conversation_made_nullable.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0006_qualid_in_conversation_made_nullable_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html">app/migrations/versions/0007_add_processing_table.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0007_add_processing_table_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html">app/migrations/versions/0008_add_intention_to_qualconversationmessage.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0008_add_intention_to_qualconversationmessage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html">app/migrations/versions/0009_index_on_createdat_and_id_on_message.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0009_index_on_createdat_and_id_on_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html">app/migrations/versions/0010_add_columns_to_qualextracteddata.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0010_add_columns_to_qualextracteddata_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html">app/migrations/versions/0011_add_confirmed_data_and_state_to_conversation.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0011_add_confirmed_data_and_state_to_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html">app/migrations/versions/0012_add_activity_id_field.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0012_add_activity_id_field_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html">app/migrations/versions/0013_add_industries_services_and_roles_.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0013_add_industries_services_and_roles__py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html">app/migrations/versions/0014_add_suggested_prompts.py</a></td>
                <td class="name left"><a href="z_08e801d2950cbe24_0014_add_suggested_prompts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html#t11">app/models/qual_conversation.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html#t11"><data value='QualConversation'>QualConversation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html">app/models/qual_conversation.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html#t12">app/models/qual_conversation_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html#t12"><data value='QualConversationMessage'>QualConversationMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html">app/models/qual_conversation_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_conversation_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html#t10">app/models/qual_document.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html#t10"><data value='QualDocument'>QualDocument</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html">app/models/qual_document.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_document_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html#t11">app/models/qual_extracted_data.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html#t11"><data value='QualExtractedData'>QualExtractedData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html">app/models/qual_extracted_data.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_extracted_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t15">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t15"><data value='JSONEncodedDict'>JSONEncodedDict</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t48">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html#t48"><data value='QualProcessingMessage'>QualProcessingMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html">app/models/qual_processing_message.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_qual_processing_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f___init___py.html">app/repositories/__init__.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t20">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html#t20"><data value='ConversationRepository'>ConversationRepository</data></a></td>
                <td>35</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="19 35">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html">app/repositories/conversation.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t19">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html#t19"><data value='ConversationMessageRepository'>ConversationMessageRepository</data></a></td>
                <td>58</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="12 58">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html">app/repositories/conversation_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_conversation_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t16">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html#t16"><data value='DocumentBlobRepository'>DocumentBlobRepository</data></a></td>
                <td>36</td>
                <td>24</td>
                <td>6</td>
                <td class="right" data-ratio="12 36">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html">app/repositories/document_blob.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_blob_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t14">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html#t14"><data value='DocumentDbRepository'>DocumentDbRepository</data></a></td>
                <td>38</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="11 38">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html">app/repositories/document_db.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_db_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t18">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html#t18"><data value='DocumentQueueRepository'>DocumentQueueRepository</data></a></td>
                <td>25</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="4 25">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html">app/repositories/document_queue.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_document_queue_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t19">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html#t19"><data value='ExtractedDataRepository'>ExtractedDataRepository</data></a></td>
                <td>46</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="9 46">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html">app/repositories/extracted_data.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_extracted_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t14">app/repositories/industry.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html#t14"><data value='IndustryRepository'>IndustryRepository</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html">app/repositories/industry.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_industry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t18">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html#t18"><data value='KXDashRepository'>KXDashRepository</data></a></td>
                <td>30</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="2 30">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html">app/repositories/kx_dash.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_kx_dash_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t13">app/repositories/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html#t13"><data value='LDMFCountriesRepository'>LDMFCountriesRepository</data></a></td>
                <td>6</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="2 6">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html">app/repositories/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_ldmf_countries_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t36">app/repositories/openai.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html#t36"><data value='OpenAIRepository'>OpenAIRepository</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>32</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html">app/repositories/openai.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_openai_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t12">app/repositories/processing_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html#t12"><data value='ProcessingMessageRepository'>ProcessingMessageRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>31</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html">app/repositories/processing_message.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_processing_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t23">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html#t23"><data value='QualsClientsRepository'>QualsClientsRepository</data></a></td>
                <td>40</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="2 40">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html">app/repositories/quals_clients.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_quals_clients_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t14">app/repositories/role.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html#t14"><data value='RoleRepository'>RoleRepository</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html">app/repositories/role.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_role_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t14">app/repositories/service.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html#t14"><data value='ServiceRepository'>ServiceRepository</data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html">app/repositories/service.py</a></td>
                <td class="name left"><a href="z_4eed7ad336a4b97f_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html">app/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html#t10">app/schemas/auth.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html#t10"><data value='SignalRToken'>SignalRToken</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html#t14">app/schemas/auth.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html#t14"><data value='User'>User</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html">app/schemas/auth.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t9">app/schemas/confirmed_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html#t9"><data value='ConfirmedData'>ConfirmedData</data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html">app/schemas/confirmed_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_confirmed_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t21">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t21"><data value='ConversationCreationRequest'>ConversationCreationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t27">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t27"><data value='ConversationResponse'>ConversationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t43">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t43"><data value='ConversationWithWelcomeMessageResponse'>ConversationWithWelcomeMessageResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t50">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html#t50"><data value='ExtendedConversationResponse'>ExtendedConversationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html">app/schemas/conversation.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f___init___py.html">app/schemas/conversation_message/__init__.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t35">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t35"><data value='MessageValidator'>MessageValidator</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t73">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t73"><data value='BaseMessageSerializer'>BaseMessageSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t86">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t86"><data value='UserMessageSerializer'>UserMessageSerializer</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t100">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t100"><data value='SystemMessageSerializer'>SystemMessageSerializer</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t121">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t121"><data value='CombinedMessageSerializer'>CombinedMessageSerializer</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t130">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t130"><data value='ConversationMessageIntentClassifierServiceResponse'>ConversationMessageIntentClassifierServiceResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t136">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html#t136"><data value='ConversationMessageProcessingResult'>ConversationMessageProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html">app/schemas/conversation_message/message.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="65 65">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t20">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t20"><data value='BaseOption'>BaseOption</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t34">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t34"><data value='ClientNameOption'>ClientNameOption</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t39">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t39"><data value='LDMFCountryOption'>LDMFCountryOption</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t44">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t44"><data value='KXDashTaskOption'>KXDashTaskOption</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t51">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html#t51"><data value='DatePickerOption'>DatePickerOption</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html">app/schemas/conversation_message/option.py</a></td>
                <td class="name left"><a href="z_41e01f802a67a65f_option_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html#t12">app/schemas/dash_task.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html#t12"><data value='DashTaskRequest'>DashTaskRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html#t35">app/schemas/dash_task.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html#t35"><data value='DashTaskResponse'>DashTaskResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html">app/schemas/dash_task.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dash_task_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html#t6">app/schemas/dates.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html#t6"><data value='DatesLLMResponse'>DatesLLMResponse</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html">app/schemas/dates.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_dates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html#t12">app/schemas/document.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html#t12"><data value='DocumentCreationRequest'>DocumentCreationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html#t20">app/schemas/document.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html#t20"><data value='DocumentResponse'>DocumentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html">app/schemas/document.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_document_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t15">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t15"><data value='ExtractedData'>ExtractedData</data></a></td>
                <td>30</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="4 30">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t89">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t89"><data value='AggregatedData'>AggregatedData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t99">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t99"><data value='FieldHandlerResponse'>FieldHandlerResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t109">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html#t109"><data value='MissingDataResponse'>MissingDataResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html">app/schemas/extracted_data.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_extracted_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html#t7">app/schemas/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html#t7"><data value='CountryData'>CountryData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html">app/schemas/ldmf_countries.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_ldmf_countries_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html#t12">app/schemas/processing.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html#t12"><data value='ProcessingStatusUpdatePayload'>ProcessingStatusUpdatePayload</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html">app/schemas/processing.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_processing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t16">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t16"><data value='ClientSearchRequest'>ClientSearchRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t24">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t24"><data value='ClientSearchItem'>ClientSearchItem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t33">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t33"><data value='ClientSearchResponse'>ClientSearchResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t42">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t42"><data value='ClientCreateRequest'>ClientCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t48">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t48"><data value='ClientComprehensive'>ClientComprehensive</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t66">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html#t66"><data value='ClientCreateResponse'>ClientCreateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html">app/schemas/quals_clients.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_quals_clients_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html#t7">app/schemas/root.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html#t7"><data value='HealthResult'>HealthResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html">app/schemas/root.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_root_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t21">app/services/auth.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html#t21"><data value='AuthService'>AuthService</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html">app/services/auth.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t12">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html#t12"><data value='IndustryDataService'>IndustryDataService</data></a></td>
                <td>11</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="1 11">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html">app/services/client_industry.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_client_industry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t36">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html#t36"><data value='ConversationService'>ConversationService</data></a></td>
                <td>43</td>
                <td>16</td>
                <td>25</td>
                <td class="right" data-ratio="27 43">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html">app/services/conversation.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t49">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html#t49"><data value='ConversationMessageService'>ConversationMessageService</data></a></td>
                <td>110</td>
                <td>82</td>
                <td>21</td>
                <td class="right" data-ratio="28 110">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html">app/services/conversation_message.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_conversation_message_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t24">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html#t24"><data value='DateValidatorService'>DateValidatorService</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html">app/services/date_validator.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_date_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t19">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html#t19"><data value='DocumentService'>DocumentService</data></a></td>
                <td>55</td>
                <td>50</td>
                <td>9</td>
                <td class="right" data-ratio="5 55">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html">app/services/document.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_document_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187___init___py.html">app/services/extracted_data/__init__.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t36">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t36"><data value='BaseFieldHandler'>BaseFieldHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t57">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t57"><data value='TokenRequiredFieldHandler'>TokenRequiredFieldHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t81">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t81"><data value='ClientNameHandler'>ClientNameHandler</data></a></td>
                <td>34</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="1 34">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t212">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t212"><data value='LDMFCountryHandler'>LDMFCountryHandler</data></a></td>
                <td>22</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="1 22">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t296">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t296"><data value='DateIntervalsHandler'>DateIntervalsHandler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t312">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t312"><data value='ObjectiveHandler'>ObjectiveHandler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t346">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html#t346"><data value='OutcomesHandler'>OutcomesHandler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html">app/services/extracted_data/handlers.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_handlers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8___init___py.html">app/services/extracted_data/parsers/__init__.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t15">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html#t15"><data value='KXDashDataParser'>KXDashDataParser</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html">app/services/extracted_data/parsers/kx_dash.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_kx_dash_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t14">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html#t14"><data value='PromptAndDocumentDataParser'>PromptAndDocumentDataParser</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html">app/services/extracted_data/parsers/prompt_and_documents.py</a></td>
                <td class="name left"><a href="z_dd1131693b77ced8_prompt_and_documents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t44">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html#t44"><data value='ExtractedDataService'>ExtractedDataService</data></a></td>
                <td>157</td>
                <td>137</td>
                <td>6</td>
                <td class="right" data-ratio="20 157">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_76e91c945518a187_service_py.html">app/services/extracted_data/service.py</a></td>
                <td class="name left"><a href="z_76e91c945518a187_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t56">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t56"><data value='IntentInfo'>IntentInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t65">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html#t65"><data value='IntentClassifierService'>IntentClassifierService</data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html">app/services/intent_classifier.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_intent_classifier_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657___init___py.html">app/services/kx_dash/__init__.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t9">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html#t9"><data value='KXDashMessageFormatter'>KXDashMessageFormatter</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html">app/services/kx_dash/formatter.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_formatter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t22">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html#t22"><data value='KXDashService'>KXDashService</data></a></td>
                <td>35</td>
                <td>33</td>
                <td>9</td>
                <td class="right" data-ratio="2 35">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html">app/services/kx_dash/service.py</a></td>
                <td class="name left"><a href="z_e7635006ae16a657_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t53">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t53"><data value='ExtractionProcessingResult'>ExtractionProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t65">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html#t65"><data value='ConversationMessageProcessor'>ConversationMessageProcessor</data></a></td>
                <td>203</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="13 203">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html">app/services/message_processor.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_message_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="54 54">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t12">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html#t12"><data value='RoleDataService'>RoleDataService</data></a></td>
                <td>11</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="1 11">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html">app/services/project_role.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_role_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t12">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html#t12"><data value='ServiceDataService'>ServiceDataService</data></a></td>
                <td>11</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="1 11">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html">app/services/project_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_project_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t19">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html#t19"><data value='SuggestedPromptsGenerator'>SuggestedPromptsGenerator</data></a></td>
                <td>51</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="38 51">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html">app/services/suggestions.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_suggestions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00___init___py.html">app/tests/__init__.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html">app/tests/conftest.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_conftest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="22 23">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5___init___py.html">app/tests/fixtures/__init__.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html">app/tests/fixtures/auth.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html">app/tests/fixtures/conversation.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_conversation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="18 19">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html">app/tests/fixtures/db.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_db_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="37 44">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t149">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html#t149"><data value='AutoCommitRepository'>AutoCommitRepository</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html">app/tests/fixtures/extracted_data_service.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_extracted_data_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="50 70">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html">app/tests/fixtures/kx_dash.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_kx_dash_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="20 35">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html">app/tests/fixtures/openai.py</a></td>
                <td class="name left"><a href="z_45f90b2413c431a5_openai_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html">app/tests/test_conversation_endpoints.py</a></td>
                <td class="name left"><a href="z_5129300603c71d00_test_conversation_endpoints_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>266</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="252 266">95%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4050</td>
                <td>1318</td>
                <td>205</td>
                <td class="right" data-ratio="2732 4050">67%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-20 15:15 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
